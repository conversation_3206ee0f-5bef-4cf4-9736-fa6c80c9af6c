const jwt = require("jsonwebtoken");
const { JWT_SECRET } = require("../constants");
const { queueApiLog } = require("./queues/apiLogQueue");

/**
 * Extract user information from JWT token
 * @param {string} token - JWT token from Authorization header
 * @returns {Object|null} - User information or null if invalid
 */
const extractUserFromToken = (token) => {
  try {
    if (!token) return null;

    // Remove 'Bearer ' prefix if present
    const cleanToken = token.startsWith("Bearer ") ? token.slice(7) : token;

    const decoded = jwt.verify(cleanToken, JWT_SECRET);
    return {
      userId: decoded.id,
    };
  } catch (error) {
    // Token is invalid or expired, return null
    return null;
  }
};

/**
 * Get client IP address from request
 * @param {Object} req - Express request object
 * @returns {string} - Client IP address
 */
const getClientIP = (req) => {
  return (
    req.ip ||
    req.connection?.remoteAddress ||
    req.socket?.remoteAddress ||
    req.headers["x-forwarded-for"]?.split(",")[0]?.trim() ||
    req.headers["x-real-ip"] ||
    "unknown"
  );
};

/**
 * Sanitize sensitive data from request/response
 * @param {Object} data - Data to sanitize
 * @returns {Object} - Sanitized data
 */
const sanitizeData = (data) => {
  if (!data || typeof data !== "object") return data;

  const sensitiveFields = [
    "password",
    "token",
    "authorization",
    "cookie",
    "session",
    "secret",
    "key",
    "private",
    "confidential",
    "auth",
  ];

  const sanitized = JSON.parse(JSON.stringify(data));

  const sanitizeObject = (obj) => {
    if (Array.isArray(obj)) {
      return obj.map((item) => sanitizeObject(item));
    } else if (obj && typeof obj === "object") {
      const result = {};
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase();
        if (sensitiveFields.some((field) => lowerKey.includes(field))) {
          result[key] = "[REDACTED]";
        } else {
          result[key] = sanitizeObject(value);
        }
      }
      return result;
    }
    return obj;
  };

  return sanitizeObject(sanitized);
};

/**
 * Determine service name from request URL
 * @param {string} url - Request URL
 * @returns {string} - Service name
 */
const getServiceName = (url) => {
  if (url.startsWith("/api/user") || url.includes("/auth")) return "auth";
  if (url.startsWith("/api/hierarchy")) return "hierarchy";
  if (url.startsWith("/api/global-master")) return "global-masters";
  if (url.startsWith("/api/communication")) return "communication";
  if (url.startsWith("/api/ashara-guide")) return "ashara-guide";
  if (url.startsWith("/api/task-management")) return "task-management";
  if (url.startsWith("/api/zones-capacity")) return "zones-capacity";
  if (url.startsWith("/api/survey")) return "survey";
  if (url.startsWith("/api/webhook")) return "webhook";
  if (url.startsWith("/api/v1")) return "public-api";
  return "unknown";
};

/**
 * Create API log data object
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {number} startTime - Request start time
 * @param {Object} responseData - Response data
 * @param {Object} error - Error object (if any)
 * @returns {Object} - API log data
 */
const createLogData = (
  req,
  res,
  startTime,
  responseData = null,
  error = null
) => {
  const endTime = Date.now();
  const duration = endTime - startTime;

  // Extract user info from token
  const authToken = req.header("Authorization");
  const userInfo = extractUserFromToken(authToken);

  // Prepare log data
  const logData = {
    // Request Information
    method: req.method,
    endpoint: req.route?.path || req.url.split("?")[0],
    fullUrl: req.originalUrl || req.url,

    // User Information
    userId: userInfo?.userId || null,

    // Request Data
    requestHeaders: sanitizeData(req.headers),
    requestBody: sanitizeData(req.body),
    queryParams: sanitizeData(req.query),

    // Response Data
    responseStatus: res.statusCode,
    responseHeaders: sanitizeData(res.getHeaders()),
    responseBody: responseData ? sanitizeData(responseData) : null,

    // Timing Information
    requestTime: new Date(startTime),
    responseTime: new Date(endTime),
    duration: duration,

    // Additional Metadata
    userAgent: req.get("User-Agent") || null,
    ipAddress: getClientIP(req),
    service: getServiceName(req.originalUrl || req.url),

    // Error Information
    error: error?.message || null,
    errorStack: error?.stack || null,

    // Success/Failure flag
    success: res.statusCode < 400 && !error,
  };

  return logData;
};

/**
 * Log API call asynchronously
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {number} startTime - Request start time
 * @param {Object} responseData - Response data (optional)
 * @param {Object} error - Error object (optional)
 */
const logApiCall = async (
  req,
  res,
  startTime,
  responseData = null,
  error = null
) => {
  try {
    const logData = createLogData(req, res, startTime, responseData, error);

    // Queue the log data for asynchronous processing
    await queueApiLog(logData);
  } catch (logError) {
    // Don't let logging errors affect the main API flow
    console.error("Failed to log API call:", logError);
  }
};

module.exports = {
  extractUserFromToken,
  getClientIP,
  sanitizeData,
  getServiceName,
  createLogData,
  logApiCall,
};
