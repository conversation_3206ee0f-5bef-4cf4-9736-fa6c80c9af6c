import { createGenericAsyncThunk } from 'redux/helper';
import { postRequest } from 'utils/axios';
import { HierarchyPrintIDCardApiUrl, HierarchyUnPrintIDCardApiUrl } from 'utils/constant';
const getPrintAPIURL = (endponints) => {
  return `${HierarchyPrintIDCardApiUrl}${endponints}`;
};
const getUnPrintAPIURL = (endponints) => {
  return `${HierarchyUnPrintIDCardApiUrl}${endponints}`;
};

const getPrintIDCard = (data, dispatch) => {
  return postRequest('hierarchy', getPrintAPIURL('get'), data, true, dispatch);
};

const getUnPrintIDCard = (data, dispatch) => {
  return postRequest('hierarchy', getUnPrintAPIURL('get'), data, true, dispatch);
};

const markAsPrint = (data, dispatch) => {
  return postRequest('hierarchy', getPrintAPIURL('get/markPrinted'), data, true, dispatch);
};

export const getPrintIDCardAction = createGenericAsyncThunk('IDCard/getPrintIDCardAction', getPrintIDCard, 'get');

export const markAsPrintAction = createGenericAsyncThunk('IDCard/markAsPrint', markAsPrint, 'get');
export const getUnPrintIDCardAction = createGenericAsyncThunk('IDCard/getUnPrintIDCardAction', getUnPrintIDCard, 'get');
