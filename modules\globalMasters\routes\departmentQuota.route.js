const { validate } = require("../../../middlewares/validation.middleware");
const { addEditDepartmentQuota, getDepartmentQuota } = require("../controllers/department.controller");
const { addEditDepartmentQuotaSchema, getDepartmentQuotaSchema } = require("../validations/department.validation");


const router = require("express").Router();

router.get("/get", getDepartmentQuota);
router.post("/add", validate(addEditDepartmentQuotaSchema, "body"), addEditDepartmentQuota);
router.put("/edit", validate(addEditDepartmentQuotaSchema, "body"), addEditDepartmentQuota);

module.exports = router;
