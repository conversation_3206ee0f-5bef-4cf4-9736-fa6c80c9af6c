const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllHierarchyPositions,
  addHierarchyPosition,
  getSingleHierarchyPosition,
  editHierarchyPosition,
  deleteHierarchyPosition,
  saveWeightageAndCount,
} = require("../controllers/hierarchyPosition.controller");

const {
  addHierarchyPositionSchema,
  getSingleHierarchyPositionSchema,
  editHierarchyPositionSchema,
  deleteHierarchyPositionSchema,
  saveWeightageAndCountSchema,
} = require("../validations/hierarchyPosition.validation");

const router = require("express").Router();

router.get("/get", getAllHierarchyPositions);

router.post(
  "/add",
  validate(addHierarchyPositionSchema, "body"),
  addHierarchyPosition
);

router.get(
  "/get/:id",
  validate(getSingleHierarchyPositionSchema, "params"),
  getSingleHierarchyPosition
);

router.put(
  "/edit",
  validate(editHierarchyPositionSchema, "body"),
  editHierarchyPosition
);

router.delete(
  "/delete/:id",
  validate(deleteHierarchyPositionSchema, "params"),
  deleteHierarchyPosition
);

router.patch(
  "/edit/save-weightage",
  validate(saveWeightageAndCountSchema, "body"),
  saveWeightageAndCount
);

module.exports = router;
