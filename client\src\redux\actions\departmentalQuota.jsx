import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';

import { HierarchyDeptQuotaApiUrl } from 'utils/constant';

const getDepartmentalQuotaURL = (endponints) => {
  return `${HierarchyDeptQuotaApiUrl}${endponints}`;
};

const getDeptQuota = (data, dispatch) => {
  return getRequest('global', getDepartmentalQuotaURL('get'), data, true, dispatch);
};

const addDeptQuota = (data, dispatch) => {
  return postRequest('global', getDepartmentalQuotaURL('add'), data, true, dispatch);
};

export const getDeptQuotaAction = createGenericAsyncThunk('deptQuota/getDeptQuotaAction', getDeptQuota, 'get', false);
export const addDeptQuotaAction = createGenericAsyncThunk('deptQuota/addDeptQuotaAction', addDeptQuota, 'post', false);
