import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, patchRequest, postRequest } from 'utils/axios';
import { HierarchySMEMappingApiUrl } from 'utils/constant';

const hierarchyMappingURL = (endponints) => {
  return `${HierarchySMEMappingApiUrl}${endponints}`;
};

const getHierarchySMEMapping = (data, dispatch) => {
  return getRequest('global', hierarchyMappingURL('get'), data, true, dispatch);
};

const postHierarchySMEMapping = (data, dispatch) => {
  return postRequest('global', hierarchyMappingURL('add'), data, true, dispatch);
};

const deleteHierarchySMEMapping = (data, dispatch) => {
  return patchRequest('global', hierarchyMappingURL('delete'), data, true, dispatch);
};

export const getSMEMappingAction = createGenericAsyncThunk('hierarchy/getSMEMappingAction', getHierarchySMEMapping, 'get', false);

export const postSMEMappingAction = createGenericAsyncThunk('hierarchy/postSMEMappingAction', postHierarchySMEMapping, 'get', false);

export const deleteSMEMappingAction = createGenericAsyncThunk('hierarchy/deleteSMEMappingAction', deleteHierarchySMEMapping, 'get', false);
