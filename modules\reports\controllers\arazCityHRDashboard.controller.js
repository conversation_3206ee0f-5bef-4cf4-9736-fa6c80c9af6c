const {
  apiH<PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const {
  ADD_SUCCESS,
  FETCH,
  UPDATE_SUCCESS,
  NOT_FOUND,
} = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const {
  redisCacheKeys,
  getCache,
  setCache,
  clearCacheByPattern,
} = require("../../../utils/redis.cache");
const {
  Araz<PERSON>ity,
  CompileList,
  KGUser,
  Interest,
  Department,
} = require("../../hierarchy/models");

const getArazCity = apiHandler(async (req, res) => {
  const { miqaatID } = req.params;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, null, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Miqaat is not active", null, res);
  }

  const arazCity = await ArazCity.find({
    miqaatID: miqaatID,
    status: true,
  }).select("name _id LDName");

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  return apiResponse(FETCH, "Araz City", arazCity, res);
});

const getCompileList = apiHandler(async (req, res) => {
  const compileList = await CompileList.find().select("name _id");

  return apiResponse(FETCH, "Compile List", compileList, res);
});

const getArazCityHrDashboardReport = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID, compileListID = [] } = req.body;

  const redisCacheKey = `${redisCacheKeys.GLOBAL_MASTER}:${
    redisCacheKeys.REPORTS
  }:ARAZCITYHRDASHBOARD:${miqaatID}:${arazCityID}:${compileListID.join(":")}`;
  const data = await getCache(redisCacheKey, miqaatID, arazCityID);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "Araz City Zone Report", data, res, true);
  }

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(arazCityID).lean();
  const departmentIDs = arazCity.departments
    ?.filter((d) => d.status === 'active') 
    .map((d) => toObjectId(d.departmentID));


  const departments = await Department.find({
    _id: { $in: departmentIDs },
    status: 'active'
  }).lean();
  
  const compileLists = await CompileList.find({
    _id: { $in: compileListID },
  }).lean();

  const kgUsers = await KGUser.find({
    miqaats: {
      $elemMatch: {
        arazCityID,
        miqaatID
      },
    },
  }).lean();

  const interestMap = {};
  const interests = await Interest.find({
    arazCityID,
    miqaatID,
    status: "not-assigned",
  }).lean();
  for (const i of interests) {
    interestMap[i.userID.toString()] = [
      i.interestOne?.departmentID?.toString(),
    ].filter(Boolean);
  }

  const rows = departments.map((dep) => {
    const depId = dep._id.toString();
    const arazDep = arazCity.departments.find(
      (d) => d.departmentID.toString() === depId
    );
    const totalHRRequirement = arazDep?.requirement || 0;

    const hierarchyUsers = kgUsers.filter((u) =>
      u.miqaats.some(
        (m) =>
          m.arazCityID.toString() === arazCityID &&
          m.miqaatID.toString() === miqaatID &&
          m.departmentID?.toString() === depId &&
          m.isActive &&
          (m.status !== "DECLINED" || m.status !== "DELETED")
      )
    );

    const hrPoolUsers = kgUsers.filter(
      (u) =>
        u.miqaats.some(
          (m) =>
            m.arazCityID.toString() === arazCityID &&
            m.miqaatID.toString() === miqaatID &&
            (!m.hierarchyPositionID || !m.isActive)
        ) && interestMap[u._id.toString()]?.includes(depId)
    );

    // Merge white list users from all compileLists for this department
    const whiteListedUserIDs = new Set();
    for (const list of compileLists) {
      const matchDept = list.departments.find(
        (d) => d.departmentID.toString() === depId
      );
      if (matchDept?.kgUsers?.length) {
        matchDept.kgUsers.forEach((u) => whiteListedUserIDs.add(u.toString()));
      }
    }

    const whiteListUserCount = [...whiteListedUserIDs].length;

    const availableInCityHierarchy = hierarchyUsers.length;
    const availableInCityHRPool = hrPoolUsers.length;
    const availableInWhiteList = whiteListUserCount;
    const total =
      availableInCityHierarchy + availableInCityHRPool + availableInWhiteList;
    const variance = totalHRRequirement - total;

    return {
      department: dep.name,
      departmentID: depId,
      totalHRRequirement,
      availableInCityHierarchy,
      availableInCityHRPool,
      availableInWhiteList,
      total,
      variance,
    };
  });

  // Non-Departmental Users
  const nonDeptHierarchy = kgUsers.filter((u) =>
    u.miqaats.some(
      (m) =>
        m.arazCityID.toString() === arazCityID 
        && m.miqaatID.toString() === miqaatID
        && !m.departmentID 
        && m.hierarchyPositionID
        && m.isActive 
        && (m.status !== "DECLINED" || m.status !== "DELETED")
    )
  );

  const nonDeptHRPool = kgUsers.filter(
    (u) =>
      u.miqaats.some(
        (m) =>
          (m.arazCityID.toString() === arazCityID &&
            m.miqaatID.toString() === miqaatID &&
            !m.departmentID &&
            !m.hierarchyPositionID) ||
          !m.isActive ||
          m.status === "DECLINED"
      ) &&
      (!interestMap[u._id.toString()] ||
        interestMap[u._id.toString()].length === 0)
  );

  const nonDeptWhiteListIDs = new Set();
  for (const list of compileLists) {
    for (const dep of list.departments) {
      dep.kgUsers?.forEach((userID) => {
        const user = kgUsers.find(
          (u) => u._id.toString() === userID.toString()
        );
        if (
          user?.miqaats.some(
            (m) =>
              m.arazCityID.toString() === arazCityID &&
              m.miqaatID.toString() === miqaatID &&
              !m.departmentID
          )
        ) {
          nonDeptWhiteListIDs.add(userID.toString());
        }
      });
    }
  }

  const nonDeptWhiteList = [...nonDeptWhiteListIDs].length;

  rows.sort((a, b) => a.department.localeCompare(b.department));

  const total = nonDeptHierarchy.length + nonDeptHRPool.length + nonDeptWhiteList;
  rows.push({
    department: "Department N/A",
    departmentID: null,
    totalHRRequirement: 0,
    availableInCityHierarchy: nonDeptHierarchy.length,
    availableInCityHRPool: nonDeptHRPool.length,
    availableInWhiteList: nonDeptWhiteList,
    total: total,
    variance: -total,
  });

  const totalSummary = rows.reduce(
    (acc, row) => {
      acc.totalHRRequirement += row.totalHRRequirement;
      acc.availableInCityHierarchy += row.availableInCityHierarchy;
      acc.availableInCityHRPool += row.availableInCityHRPool;
      acc.availableInWhiteList += row.availableInWhiteList;
      acc.total += row.total;
      acc.variance += row.variance;
      return acc;
    },
    {
      totalHRRequirement: 0,
      availableInCityHierarchy: 0,
      availableInCityHRPool: 0,
      availableInWhiteList: 0,
      total: 0,
      variance: 0,
    }
  );

  const result = {
    columns: [
      { title: "Department", name: "department" },
      { title: "Total HR Requirement", name: "totalHRRequirement" },
      {
        title: "Available in City Hierarchy",
        name: "availableInCityHierarchy",
      },
      { title: "Available In City HR Pool", name: "availableInCityHRPool" },
      { title: "Available In White List(s)", name: "availableInWhiteList" },
      { title: "Total", name: "total" },
      { title: "Variance", name: "variance" },
    ],
    rows,
    total: totalSummary,
  };

  apiResponse(FETCH, "Araz City HR Dashboard Report Fetched", result, res);
  await setCache(redisCacheKey, result, miqaatID, arazCityID);
});

const saveArazCityHrDashboardReport = apiHandler(async (req, res) => {
  const { arazCityID, departments } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(null, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(arazCityID);
  if (!arazCity) {
    return apiResponse(NOT_FOUND, "Araz City not found", null, res);
  }

  arazCity.departments = arazCity.departments.map((dep) => {
    const match = departments.find(
      (d) => d.departmentID.toString() === dep.departmentID.toString()
    );
    if (match && typeof match.requirement === "number") {
      return { ...dep, requirement: match.requirement };
    }
    return dep;
  });

  await arazCity.save();

  await clearCacheByPattern(`*`,arazCity.miqaatID, arazCityID);

  return apiResponse(UPDATE_SUCCESS, "Araz City HR Dashboard", {}, res);
});

module.exports = {
  getArazCity,
  getCompileList,
  getArazCityHrDashboardReport,
  saveArazCityHrDashboardReport,
};
