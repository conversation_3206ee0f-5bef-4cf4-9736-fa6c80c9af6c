import dayjs from 'dayjs';
import { createGenericAsyncThunk } from 'redux/helper';
import { postRequest } from 'utils/axios';

import { CommunicationEmailReportApiUrl } from 'utils/constant';

const getEmailReportURL = (endponints) => {
  return `${CommunicationEmailReportApiUrl}${endponints}`;
};

const getEmailReport = (data, dispatch) => {
  return postRequest('communication', getEmailReportURL('get'), data, true, dispatch);
};

export const getEmailReportAction = createGenericAsyncThunk('emailReport/getEmailReportAction', getEmailReport, 'get', false);
