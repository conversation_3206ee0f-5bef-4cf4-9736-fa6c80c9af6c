const {
  addEditDocumentSchema,
  getSingleDocumentSchema,
  deleteDocumentSchema,
  getDownloadURLSchema,
  getAllDocumentSchema,
} = require("../validations/document.validation");
const { upload } = require("../../../middlewares/multer.middleware");
const fileUpload = upload("document_manager_files");

const {
  addDocument,
  getSingleDocument,
  editDocument,
  deleteDocument,
  uploadDocument,
  getDocumentType,
  getDocumentURL,
  getAllDocuments
} = require("../controllers/document.controller");
const { validate } = require("../../../middlewares/validation.middleware");

const router = require("express").Router();


router.post("/add", validate(addEditDocumentSchema, "body"), addDocument);
router.post("/get",validate(getAllDocumentSchema, "body"), getAllDocuments);
router.get("/get/document-type", getDocumentType);
router.get("/get/document-url", validate(getDownloadURLSchema, "body"), getDocumentURL);
router.get("/get/:id", validate(getSingleDocumentSchema, "params"), getSingleDocument);
router.put("/edit/:id", validate(addEditDocumentSchema, "body"), editDocument);
router.delete("/delete/:id", validate(deleteDocumentSchema, "params"), deleteDocument);

router.post(
  "/add/upload-document",
  fileUpload.array("file"),
  uploadDocument
);

module.exports = router;
