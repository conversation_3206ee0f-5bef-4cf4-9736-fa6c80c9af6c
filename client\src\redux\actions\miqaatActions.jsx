import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyMiqaatApiUrl } from 'utils/constant';

const getMiqaatURL = (endponints) => {
  return `${HierarchyMiqaatApiUrl}${endponints}`;
};

const getAllMiqaat = (data, dispatch) => {
  return getRequest('global', getMiqaatURL('get'), data, true, dispatch);
};

const getAllMiqaats = (data, dispatch) => {
  return getRequest('global', getMiqaatURL(`get?status=${data}`), data, true, dispatch);
};

const getSingleMiqaat = (data, dispatch) => {
  return getRequest('global', getMiqaatURL(`get/${data}`), data, true, dispatch, 'formdata');
};
const addMiqaat = (data, dispatch) => {
  return postRequest('global', getMiqaatURL('add'), data, true, dispatch, 'formdata');
};

const updateMiqaat = (data, dispatch) => {
  return putRequest('global', getMiqaatURL('edit'), data, true, dispatch, 'formdata');
};

const deleteMiqaat = (data, dispatch) => {
  return deleletRequest('global', getMiqaatURL(`delete/${data}`), data, true, dispatch, 'formdata');
};

export const getMiqaatsAction = createGenericAsyncThunk('Miqaat/getMiqaatsAction', getAllMiqaat, 'get');

export const getSingleMiqaatAction = createGenericAsyncThunk('Miqaat/getSingleMiqaatAction', getSingleMiqaat, 'get');

export const addMiqaatAction = createGenericAsyncThunk('Miqaat/addMiqaatAction', addMiqaat);

export const updateMiqaatAction = createGenericAsyncThunk('Miqaat/updateMiqaatAction', updateMiqaat);

export const deleteMiqaatAction = createGenericAsyncThunk('Miqaat/deleteMiqaatAction', deleteMiqaat);

export const getAllMiqaatsAction = createGenericAsyncThunk('Miqaat/getAllMiqaatsAction', getAllMiqaats, 'get');
