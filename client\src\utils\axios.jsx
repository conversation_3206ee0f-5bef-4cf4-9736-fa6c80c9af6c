import axios from 'axios';
import { get } from 'lodash';
import { getItemFromLocalStorage, logoutUser, showAlert } from './helper';
import { BASE_URL } from 'config';
import { getApiUrl } from 'api.config';

const axiosInstance = axios.create();

const service = (config, includeToken = true, dispatch, contentType) => {
  let token = getItemFromLocalStorage('token');
  let permissionToken = getItemFromLocalStorage('permissionTokenID', '');
  if (includeToken) {
    config.headers = {
      ...config.headers,
      authorization: token,
      Permissions: permissionToken
    };
  }
  if (contentType === 'formdata') {
    config.headers = {
      ...config.headers,
      'Content-Type': 'multipart/form-data'
    };
  }
  axiosInstance.interceptors.response.use(
    (response) => {
      return response;
    },
    function (error) {
      return handleErrors(error, dispatch);
    }
  );

  return axiosInstance(config);
};

export const handleErrors = async (error, dispatch) => {
  if (!error) {
    return Promise.reject('Something went wrong');
  }

  const { response, request, message, data } = error;

  if (response) {
    const { status, data } = response;
    if (status === 401) {
      showAlert(dispatch, false, 'Unauthorized access', true);
      await logoutUser(dispatch);

      return Promise.reject({ data: data } || 'Unauthorized access');
    } else if (status === 404) {
      return Promise.reject({ data: data } || 'Resource not found');
    } else if (status === 500) {
      return Promise.reject({ data: data } || 'Internal server error');
    } else if (status === 400) {
      if (data?.error && typeof data.error === 'string') {
        return Promise.reject({
          data: {
            message: data.error,
            error: true
          }
        });
      } else if (data?.message && typeof data.message === 'string') {
        return Promise.reject({ data });
      } else {
        return Promise.reject({ data: { message: 'Unknown error occurred', error: true } });
      }
    } else {
      return Promise.reject({ data: data } || 'Server error');
    }
  } else if (request) {
    return Promise.reject('No response from server');
  } else {
    return Promise.reject(error || 'Unknown error occurred');
  }
};

/**
 * Makes a POST request.
 * @param {string | null} serviceName - The microservice to target (e.g., 'hierarchy'). If null, 'endpoint' is treated as a full URL.
 * @param {string} endpoint - The endpoint path OR the full URL if serviceName is null.
 * @param {any} data - The request payload.
 */
const postRequest = (serviceName, endpoint, data, includeToken = true, dispatch, contentType = null) => {
  // If serviceName is null, endpoint is the full URL. Otherwise, build it.
  let url = '';
  try {
    url = serviceName ? getApiUrl(serviceName, endpoint) : endpoint;
  } catch (error) {
    return Promise.reject('Error in postRequest');
  }

  return service(
    {
      method: 'POST',
      url: url, // Use the final URL
      data
    },
    includeToken,
    dispatch,
    contentType
  )
    .then(async (res) => res)
    .catch((err) => err);
};

/**
 * Makes a GET request.
 * @param {string | null} serviceName - The microservice to target. If null, 'endpoint' is a full URL.
 * @param {string} endpoint - The endpoint path or full URL.
 */
const getRequest = (serviceName, endpoint, navigate, includeToken = true, dispatch) => {
  const url = serviceName ? getApiUrl(serviceName, endpoint) : endpoint;

  return service(
    {
      method: 'GET',
      url: url
    },
    includeToken,
    dispatch
  )
    .then(async (res) => res)
    .catch((err) => err);
};

const patchRequest = (serviceName, endpoint, data, includeToken = true, dispatch, contentType = null) => {
  const url = serviceName ? getApiUrl(serviceName, endpoint) : endpoint;
  return service(
    {
      method: 'PATCH',
      url,
      data
    },
    includeToken,
    dispatch,
    contentType
  )
    .then(async (res) => {
      return res;
    })
    .catch((err) => {
      return err;
    });
};

const putRequest = (serviceName, endpoint, data, includeToken = true, dispatch, contentType = null) => {
  const url = serviceName ? getApiUrl(serviceName, endpoint) : endpoint;
  return service(
    {
      method: 'PUT',
      url,
      data
    },
    includeToken,
    dispatch,
    contentType
  )
    .then(async (res) => {
      return res;
    })
    .catch((err) => {
      return err;
    });
};

const deleletRequest = (serviceName, endpoint, data, includeToken = true, dispatch) => {
  const url = serviceName ? getApiUrl(serviceName, endpoint) : endpoint;
  return service(
    {
      method: 'DELETE',
      url,
      data
    },
    includeToken,
    dispatch
  )
    .then(async (res) => {
      return res;
    })
    .catch((err) => {
      return err;
    });
};

export { postRequest, getRequest, deleletRequest, patchRequest, putRequest };
