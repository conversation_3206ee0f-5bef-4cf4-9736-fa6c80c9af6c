const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  DELETE_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { WaazVenueSuitability, WaazVenue } = require("../../zonesCapacity/models");

const getAllWaazVenueSuitabilities = apiHandler(async (req, res) => {
  const waazVenueSuitabilities = await WaazVenueSuitability.find().sort({
    _id: -1,
  });
  return apiResponse(
    FETCH,
    "Waaz Venue Suitabilities",
    waazVenueSuitabilities,
    res
  );
});

const addEditWaazVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = { uniqueName: data.uniqueName };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingWaazVenueSuitability = await WaazVenueSuitability.findOne(
    uniqueNameQueryObject
  );

  if (existingWaazVenueSuitability) {
    return apiError(
      CUSTOM_ERROR,
      "Waaz Venue Suitability with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;

    const newWaazVenueSuitability = new WaazVenueSuitability(data);

    let savedWaazVenueSuitabilityData = await newWaazVenueSuitability.save();

    return apiResponse(
      ADD_SUCCESS,
      "Waaz Venue Suitability",
      savedWaazVenueSuitabilityData,
      res
    );
  } else {
    data.updatedBy = req.user._id;

    let updatedWaazVenueSuitabilityData =
      await WaazVenueSuitability.findByIdAndUpdate(id, data, {
        new: true,
        runValidators: true,
      });

    if (!updatedWaazVenueSuitabilityData) {
      return apiError(NOT_FOUND, "Waaz Venue Suitability", null, res);
    }

    return apiResponse(
      UPDATE_SUCCESS,
      "Waaz Venue Suitability",
      updatedWaazVenueSuitabilityData,
      res
    );
  }
});

const getSingleWaazVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.params;
  const waazVenueSuitabilityData = await WaazVenueSuitability.findById(id);

  if (!waazVenueSuitabilityData)
    return apiError(NOT_FOUND, "Waaz Venue Suitability", null, res);

  return apiResponse(
    FETCH,
    "Waaz Venue Suitability",
    waazVenueSuitabilityData,
    res
  );
});

const deleteWaazVenueSuitability = apiHandler(async (req, res) => {
  const { id } = req.params;
  const isUsedInSeating = await WaazVenue.findOne({
    waazVenueSuitabilityID: toObjectId(id),
  });
  if (isUsedInSeating) {
    return apiError(DELETE_ERROR, "Waaz Venue Suitability is used in Waaz Seating", null, res);
  }
  const waazVenueSuitabilityData = await WaazVenueSuitability.findOneAndDelete({
    _id: id,
  });

  if (!waazVenueSuitabilityData)
    return apiError(NOT_FOUND, "Waaz Venue Suitability", null, res);

  return apiResponse(DELETE_SUCCESS, "Waaz Venue Suitability", null, res);
});

module.exports = {
  getAllWaazVenueSuitabilities,
  addEditWaazVenueSuitability,
  getSingleWaazVenueSuitability,
  deleteWaazVenueSuitability,
};
