import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest } from 'utils/axios';

import { CommunicationComposeApiUrl } from 'utils/constant';
import { getInboxURL } from './inboxAction';

const getComposeURL = (endPoints) => {
  return `${CommunicationComposeApiUrl}${endPoints}`;
};

const composeEmail = (data, dispatch) => {
  return postRequest('communication', getComposeURL('get/message'), data, true, dispatch);
};

const updateScheduledMails = (data, dispatch) => {
  return postRequest('communication', getComposeURL('get/edit/message'), data, true, dispatch);
};

const getMessageType = (data, dispatch) => {
  return getRequest('communication', getComposeURL('get/messageType'), data, true, dispatch);
};

const getMessageRecipients = (data, dispatch) => {
  return postRequest('communication', getComposeURL('get/recipients'), data, true, dispatch);
};

const deleteScheduledMails = (data, dispatch) => {
  let payload = {
    miqaatID: data?.miqaatID,
    arazCityID: data?.arazCityID
  };
  return patchRequest('communication', getInboxURL(`get/delete/message/${data?.id}`), payload, true, dispatch);
};

export const composeEmailAction = createGenericAsyncThunk('compose/composeEmailAction', composeEmail);

export const updateScheduledMailsAction = createGenericAsyncThunk('compose/updateScheduledMailsAction', updateScheduledMails);

export const getMessageTypeAction = createGenericAsyncThunk('compose/getMessageTypeAction', getMessageType, 'get', false);

export const getMessageRecipientsAction = createGenericAsyncThunk('compose/getMessageRecipientsAction', getMessageRecipients, 'get', false);

export const deletetScheduledMailsAction = createGenericAsyncThunk('compose/deleteScheduledMailsAction', deleteScheduledMails);
