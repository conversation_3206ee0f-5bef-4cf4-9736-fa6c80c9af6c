import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { getRequest, postRequest } from 'utils/axios';
import { setItemToLocalStorage } from 'utils/helper';
import { ALERT_SUCCESS } from './alertReducer';
import { GetPermissionsApiUrl, HierarchyDashboardPageUrl, loginApiUrl } from 'utils/constant';
import { createAddCaseHand<PERSON>, createGenericAsyncThunk } from 'redux/helper';
import { permission } from 'process';
import { getApiUrl } from 'api.config';

export const loginUser = createAsyncThunk(loginApiUrl, async ({ Token, SID, OneLogin, DT }, { rejectWithValue, dispatch }) => {
  try {
    const response = await postRequest('auth', loginApiUrl, { Token, SID, OneLogin, DT }, false, dispatch);

    const { success, message, error, data } = get(response, 'data', {});

    if (response) {
      if (success) {
        dispatch({
          type: ALERT_SUCCESS,
          payload: { success: true, message: message || 'Something went wrong', error: false }
        });
        if (data?.token) {
          let token = data?.token;
          setItemToLocalStorage('token', token);
          dispatch(storeToken(token));
        }

        return { data, success };
      } else {
        dispatch({
          type: ALERT_SUCCESS,
          payload: { success: false, message: message || 'Something went wrong', error: true }
        });
        return { data: data || response?.data || [], success: false };
      }
    } else {
      return { data: [], success: false };
    }
  } catch (error) {
    dispatch({
      type: ALERT_SUCCESS,
      payload: { success: false, message: 'Something went wrong', error: true }
    });
    return { data: [], success: false };
  }
});

const getPermissions = (data, dispatch) => {
  return postRequest('auth', GetPermissionsApiUrl, data, true, dispatch);
};

export const getPermissionsAction = createGenericAsyncThunk('permissions/getPermissionsAction', getPermissions, 'get');

const authSlice = createSlice({
  name: 'auth',
  initialState: {
    user: null,
    token: '',
    permissionToken: '',
    selectedModule: '',
    status: 'idle',
    loading: false,
    error: null
  },
  reducers: {
    storeToken: (state, action) => {
      state.token = action.payload;
    },
    storePermissionToken: (state, action) => {
      state.permissionToken = action.payload;
    },
    storeSelectedModule: (state, action) => {
      state.selectedModule = action.payload;
    },
    storeMiqaatAndArazCityAction: (state, action) => {
      state.selectedMiqaatAndArazCity = action.payload;
    },
    logoutSuccess: (state) => {
      state.user = null;
      state.token = '';
      state.permissionToken = '';
      state.selectedModule = '';
      state.status = 'idle';
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.status = 'loading';
        state.loading = true;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.status = 'succeeded';
        state.loading = false;
        state.user = action.payload;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.status = 'failed';
        state.loading = false;
        state.error = action.payload;
      });
    createAddCaseHandler(builder, getPermissionsAction);
  }
});

export const selectUser = (state) => state.auth.user;
export const selectAuthStatus = (state) => state.auth.status;
export const selectAuthError = (state) => state.auth.error;
export const { storeToken, logoutSuccess, storeMiqaatAndArazCityAction, storePermissionToken, storeSelectedModule } = authSlice.actions;
export default authSlice.reducer;
