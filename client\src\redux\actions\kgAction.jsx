import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { addOneSignalDeviceApiUrl, getKGITSIDDetailApiUrl, HierarchyKGApiUrl, UpdateConsentStatusApiUrl } from 'utils/constant';
import { getUserApiURL } from './profileAction';

const getKGURL = (endponints) => {
  return `${HierarchyKGApiUrl}${endponints}`;
};

const getAllKG = (data, dispatch) => {
  return postRequest('hierarchy', getKGURL('get/by-pagination'), data, true, dispatch);
};

const getAllKGExportRecord = (data, dispatch) => {
  return postRequest('hierarchy', getKGURL('get/by-pagination'), data, true, dispatch);
};

const syncITSData = (data, dispatch) => {
  return postRequest('hierarchy', getUserApiURL('edit/its-sync'), data, true, dispatch);
};

const getSingleKG = (data, dispatch) => {
  return postRequest('hierarchy', getKGURL('get/single-kg'), data, true, dispatch);
};

const getKGITSDetail = (data, dispatch) => {
  return getRequest('hierarchy', getKGITSIDDetailApiUrl(data), data, true, dispatch);
};
const addKG = (data, dispatch) => {
  return postRequest('hierarchy', getKGURL('add'), data, true, dispatch);
};

const updateKG = (data, dispatch) => {
  return putRequest('hierarchy', getKGURL('edit'), data, true, dispatch);
};

const deleteKG = (data, dispatch) => {
  return deleletRequest('hierarchy', getKGURL(`delete`), data, true, dispatch);
};

const addOneSignalDevice = (data, dispatch) => {
  return postRequest('hierarchy', addOneSignalDeviceApiUrl, data, true, dispatch);
};

const updateConsentStatus = (data, dispatch) => {
  return patchRequest('hierarchy', UpdateConsentStatusApiUrl, data, true, dispatch);
};

const getKGByITS = (data, dispatch) => {
  return postRequest('hierarchy', getKGURL('get/kg-users-by-its'), data, true, dispatch);
};

export const updateConsentStatusAction = createGenericAsyncThunk('permissions/updateConsentStatusAction', updateConsentStatus);

export const getAllKGAction = createGenericAsyncThunk('KG/getAllKG', getAllKG, 'get');

export const getSingleKGAction = createGenericAsyncThunk('KG/getSingleKGAction', getSingleKG, 'get');

export const getKGITSDetailAction = createGenericAsyncThunk('KG/getKGITSDetailAction', getKGITSDetail);

export const addKGAction = createGenericAsyncThunk('KG/addKGAction', addKG);

export const updateKGAction = createGenericAsyncThunk('KG/updateKGAction', updateKG);

export const deleteKGAction = createGenericAsyncThunk('KG/deleteKGAction', deleteKG);

export const syncITSDataAction = createGenericAsyncThunk('sync/syncITSDataAction', syncITSData, 'get');

export const addOneSignalDeviceAction = createGenericAsyncThunk('oneSignal/addOneSignalDeviceAction', addOneSignalDevice, 'get');

export const getKGByITSAction = createGenericAsyncThunk('oneSignal/getKGByITSAction', getKGByITS, 'get');

export const getAllKGExportRecordAction = createGenericAsyncThunk('kg/getAllKGExportRecord', getAllKGExportRecord, 'get');
