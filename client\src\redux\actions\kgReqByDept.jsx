import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest, putRequest } from 'utils/axios';

import { HierarchyKgRequisitionByDeptApiUrl, HierarchyKgRequisitionByZoneApiUrl } from 'utils/constant';

const getByDeptURL = (endponints) => {
  return `${HierarchyKgRequisitionByDeptApiUrl}${endponints}`;
};
const getByZoneURL = (endponints) => {
  return `${HierarchyKgRequisitionByZoneApiUrl}${endponints}`;
};

const getArazCityZone = (data, dispatch) => {
  return getRequest(
    'hierarchy',
    getByDeptURL(`get/araz-city-zone?miqaatID=${data.miqaatId}&arazCityID=${data.arazCityId}`),
    data,
    true,
    dispatch
  );
};

const getKgRequisitionByDept = (data, dispatch) => {
  return postRequest('hierarchy', getByDeptURL('get/kg-requisition-by-department'), data, true, dispatch);
};

const getKgRequisitionByZone = (data, dispatch) => {
  return postRequest('hierarchy', getByZoneURL('/get/kg-requisition-by-zone'), data, true, dispatch);
};

const addKgRequisitionByDept = (data, dispatch) => {
  return putRequest('hierarchy', getByDeptURL('add/kg-requisition-by-department'), data, true, dispatch);
};
const addKgRequisitionByZone = (data, dispatch) => {
  return putRequest('hierarchy', getByZoneURL('/add/kg-requisition-by-zone'), data, true, dispatch);
};

export const getArazCityZoneAction = createGenericAsyncThunk('emailReport/getArazCityZoneAction', getArazCityZone, 'get', false);
export const getKgRequisitionByDeptAction = createGenericAsyncThunk(
  'emailReport/getKgRequisitionByDeptAction',
  getKgRequisitionByDept,
  'get',
  false
);
export const addKgRequisitionByDeptAction = createGenericAsyncThunk(
  'emailReport/addKgRequisitionByDeptAction',
  addKgRequisitionByDept,
  'get',
  false
);

export const addKgRequisitionByZoneAction = createGenericAsyncThunk(
  'emailReport/addKgRequisitionByZoneAction',
  addKgRequisitionByZone,
  'get',
  false
);

export const getKgRequisitionByZoneAction = createGenericAsyncThunk(
  'emailReport/getKgRequisitionByZoneAction',
  getKgRequisitionByZone,
  'get',
  false
);
