const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllFunctions,
  addFunction,
  getSingleFunction,
  editFunction,
  deleteFunction,
  getFunctionsByDepartment,
  getAllDepartmentFunctions,
} = require("../controllers/function.controller");

const {
  addFunctionSchema,
  getSingleFunctionSchema,
  editFunctionSchema,
  deleteFunctionSchema,
  getFunctionsByDepartmentSchema,
} = require("../validations/function.validation");

const router = require("express").Router();

router.get("/get", getAllFunctions);

router.post("/add", validate(addFunctionSchema, "body"), addFunction);

router.get(
  "/get/:id",
  validate(getSingleFunctionSchema, "params"),
  getSingleFunction
);

router.put("/edit", validate(editFunctionSchema, "body"), editFunction);

router.delete(
  "/delete/:id",
  validate(deleteFunctionSchema, "params"),
  deleteFunction
);

router.get(
  "/get/by-department/:id",
  validate(getFunctionsByDepartmentSchema, "params"),
  getFunctionsByDepartment
);

router.get(
  "/get/department-functions/all",
  getAllDepartmentFunctions
);

module.exports = router;
