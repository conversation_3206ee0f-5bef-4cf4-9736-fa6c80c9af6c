import { kgUsers } from 'pages/component/hierarchy/constant';
import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest } from 'utils/axios';

const compileWhiteListURL = (endponints) => {
  return `hierarchy/compile-user-list/${endponints}`;
};

const getCompileWhiteLists = (data, dispatch) => {
  return getRequest('hierarchy', compileWhiteListURL('get'), data, true, dispatch);
};

const getApprovedWhiteList = (data, dispatch) => {
  return getRequest('hierarchy', 'hierarchy/approved-compile-lists/get', data, true, dispatch);
};

const getArazCities = (data, dispatch) => {
  return getRequest('hierarchy', compileWhiteListURL('get/araz-cities'), data, true, dispatch);
};

const getDepartmentWiseUsers = (data, dispatch) => {
  return postRequest('hierarchy', `${compileWhiteListURL('get')}/department-users`, data, true, dispatch);
};

const getUsersByITS = (data, dispatch) => {
  return postRequest('hierarchy', `${compileWhiteListURL('get')}/import-its-users`, data, true, dispatch);
};

const getSingleCompileWhiteList = (data, dispatch) => {
  return getRequest('hierarchy', `${compileWhiteListURL('get')}/${data.id}`, data, true, dispatch);
};

const addCompileUserList = (data, dispatch) => {
  return patchRequest('hierarchy', compileWhiteListURL('add'), data, true, dispatch);
};
const addCompileUserListByQuota = (data, dispatch) => {
  return patchRequest('hierarchy', compileWhiteListURL('add/department-quota'), data, true, dispatch);
};

const editCompileList = (data, dispatch) => {
  return patchRequest('hierarchy', compileWhiteListURL('edit'), data, true, dispatch);
};

const editCompileListUser = (data, dispatch) => {
  return patchRequest('hierarchy', compileWhiteListURL('edit/user'), data, true, dispatch);
};

const deleteWhiteList = (data, dispatch) => {
  return deleletRequest('hierarchy', `${compileWhiteListURL(`delete/${data.id}`)}`, data, true, dispatch);
};

const deleteCompileListUser = (data, dispatch) => {
  return patchRequest('hierarchy', `${compileWhiteListURL(`delete/user`)}`, data, true, dispatch);
};

const getRazaCompileList = (data, dispatch) => {
  return getRequest('hierarchy', `hierarchy/raza-list/get/${data}`, data, true, dispatch);
};

const addRazaUserInHierarchy = (data, dispatch) => {
  return patchRequest('hierarchy', 'hierarchy/raza-list/update', data, true, dispatch);
};

export const getCompileWhiteListsAction = createGenericAsyncThunk(
  'hierarchy/getCompileWhiteListsAction',
  getCompileWhiteLists,
  'get',
  false
);

export const getApprovedWhiteListAction = createGenericAsyncThunk(
  'hierarchy/getApprovedWhiteListAction',
  getApprovedWhiteList,
  'get',
  false
);

export const getArazCitiesAction = createGenericAsyncThunk('hierarchy/getArazCitiesAction', getArazCities, 'get', false);

export const getDepartmentWiseUsersAction = createGenericAsyncThunk(
  'hierarchy/getDepartmentWiseUsersAction',
  getDepartmentWiseUsers,
  'get',
  false
);

export const getUsersByITSAction = createGenericAsyncThunk('hierarchy/getUsersByITSAction', getUsersByITS, 'get', false);

export const getSingleCompileWhiteListAction = createGenericAsyncThunk(
  'hierarchy/getSingleCompileWhiteListAction',
  getSingleCompileWhiteList,
  'get',
  false
);

export const addCompileUserListAction = createGenericAsyncThunk('hierarchy/addCompileUserListAction', addCompileUserList, 'get', false);

export const addCompileUserListByQuotaAction = createGenericAsyncThunk(
  'hierarchy/addCompileUserListByQuotaAction',
  addCompileUserListByQuota,
  'get',
  false
);

export const editCompileListAction = createGenericAsyncThunk('hierarchy/editCompileListAction', editCompileList, 'get', false);

export const editCompileListUserAction = createGenericAsyncThunk('hierarchy/editCompileListUserAction', editCompileListUser, 'get', false);

export const deleteWhiteListAction = createGenericAsyncThunk('hierarchy/deleteRecommendedUserAction', deleteWhiteList, 'get', false);

export const deleteCompileListUserAction = createGenericAsyncThunk(
  'hierarchy/deleteCompileListUserAction',
  deleteCompileListUser,
  'get',
  false
);

export const getRazaCompileListAction = createGenericAsyncThunk('hierarchy/getRazaCompileListAction', getRazaCompileList, 'get', false);

export const addRazaUserInHierarchyAction = createGenericAsyncThunk(
  'hierarchy/addRazaUserInHierarchyAction',
  addRazaUserInHierarchy,
  'get',
  false
);
