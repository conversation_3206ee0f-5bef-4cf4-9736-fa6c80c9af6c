import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityWaazVenueSuitabilityApiUrl } from 'utils/constant';

const getWaazVenueSuitabilityURL = (endponints) => {
  return `${ZoneCapacityWaazVenueSuitabilityApiUrl}${endponints}`;
};

const getAllWaazVenueSuitability = (data, dispatch) => {
  return getRequest('global', getWaazVenueSuitabilityURL('get'), data, true, dispatch);
};

const getSingleWaazVenueSuitability = (data, dispatch) => {
  return getRequest('global', getWaazVenueSuitabilityURL(`get/${data}`), data, true, dispatch);
};

const addWaazVenueSuitability = (data, dispatch) => {
  return postRequest('global', getWaazVenueSuitabilityURL('add'), data, true, dispatch);
};

const updateWaazVenueSuitability = (data, dispatch) => {
  return putRequest('global', getWaazVenueSuitabilityURL('edit'), data, true, dispatch);
};

const deleteWaazVenueSuitability = (data, dispatch) => {
  return deleletRequest('global', getWaazVenueSuitabilityURL(`delete/${data}`), data, true, dispatch);
};

export const getAllWaazVenueSuitabilityAction = createGenericAsyncThunk(
  'waazVenueSuitability/getAllWaazVenueSuitabilityAction',
  getAllWaazVenueSuitability,
  'get'
);

export const getSingleWaazVenueSuitabilityAction = createGenericAsyncThunk(
  'waazVenueSuitability/getSingleWaazVenueSuitabilityAction',
  getSingleWaazVenueSuitability,
  'get'
);

export const addWaazVenueSuitabilityAction = createGenericAsyncThunk(
  'waazVenueSuitability/addWaazVenueSuitabilityAction',
  addWaazVenueSuitability
);

export const updateWaazVenueSuitabilityAction = createGenericAsyncThunk(
  'waazVenueSuitability/updateWaazVenueSuitabilityAction',
  updateWaazVenueSuitability
);

export const deleteWaazVenueSuitabilityAction = createGenericAsyncThunk(
  'waazVenueSuitability/deleteWaazVenueSuitabilityAction',
  deleteWaazVenueSuitability
);
