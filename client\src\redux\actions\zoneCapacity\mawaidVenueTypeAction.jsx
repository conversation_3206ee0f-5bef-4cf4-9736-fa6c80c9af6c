import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityMawaidVenueTypeApiUrl } from 'utils/constant';

const getMawaidVenueTypeURL = (endponints) => {
  return `${ZoneCapacityMawaidVenueTypeApiUrl}${endponints}`;
};

const getAllMawaidVenueType = (data, dispatch) => {
  return getRequest('global', getMawaidVenueTypeURL('get'), data, true, dispatch);
};

const getSingleMawaidVenueType = (data, dispatch) => {
  return getRequest('global', getMawaidVenueTypeURL(`get/${data}`), data, true, dispatch);
};

const addMawaidVenueType = (data, dispatch) => {
  return postRequest('global', getMawaidVenueTypeURL('add'), data, true, dispatch);
};

const updateMawaidVenueType = (data, dispatch) => {
  return putRequest('global', getMawaidVenueTypeURL('edit'), data, true, dispatch);
};

const deleteMawaidVenueType = (data, dispatch) => {
  return deleletRequest('global', getMawaidVenueTypeURL(`delete/${data}`), data, true, dispatch);
};

export const getAllMawaidVenueTypeAction = createGenericAsyncThunk(
  'MawaidVenueType/getAllMawaidVenueTypeAction',
  getAllMawaidVenueType,
  'get'
);

export const getSingleMawaidVenueTypeAction = createGenericAsyncThunk(
  'MawaidVenueType/getSingleMawaidVenueTypeAction',
  getSingleMawaidVenueType,
  'get'
);

export const addMawaidVenueTypeAction = createGenericAsyncThunk('MawaidVenueType/addMawaidVenueTypeAction', addMawaidVenueType);

export const updateMawaidVenueTypeAction = createGenericAsyncThunk('MawaidVenueType/updateMawaidVenueTypeAction', updateMawaidVenueType);

export const deleteMawaidVenueTypeAction = createGenericAsyncThunk('MawaidVenueType/deleteMawaidVenueTypeAction', deleteMawaidVenueType);
