# AMS API Gateway

A comprehensive API Gateway for the AMS (Attendance Management System) microservices architecture. The gateway runs on port 8000 and acts as a single entry point for all your microservices.

## 🚀 Features

### 🌐 **Single Entry Point**
- All API requests go through port 8000
- Automatic routing to appropriate microservices
- Centralized request/response handling

### 🔍 **Service Discovery**
- Automatic service registration
- Health checking every 30 seconds
- Load balancing with multiple strategies

### 🛡️ **Security & Authentication**
- JWT token validation
- Rate limiting (1000 requests per 15 minutes per IP)
- CORS handling
- Security headers with Helmet

### ⚡ **Performance**
- Request compression
- Connection pooling
- Timeout handling (30 seconds)
- Retry logic for failed requests

### 📊 **Monitoring & Logging**
- Request/response logging
- Health status monitoring
- Performance metrics
- Service status dashboard

## 🏗️ Architecture

```
Client Request → Gateway (Port 8000) → Service Discovery → Target Service
                     ↓
                Response ← Load Balancer ← Health Check ← Service Instance
```

## 📋 Service Configuration

The gateway automatically routes requests to these services:

| Service | Port | Routes | Health Check |
|---------|------|--------|--------------|
| Authentication | 3006 | `/api/user`, `/api/auth` | `/health` |
| Hierarchy | 3001 | `/api/hierarchy`, `/api/hr` | `/health` |
| Global Masters | 3007 | `/api/global-master`, `/api/global` | `/health` |
| Communication | 3002 | `/api/communication`, `/api/message` | `/health` |
| Task Management | 3004 | `/api/task-management`, `/api/task` | `/health` |
| Zones Capacity | 3003 | `/api/zones-capacity`, `/api/zones` | `/health` |
| Survey | 3005 | `/api/survey` | `/health` |

## 🚀 Getting Started

### Prerequisites
```bash
npm install express-rate-limit helmet http-proxy-middleware
```

### Start the Gateway
```bash
# Using npm script
npm run start:gateway

# Or directly
node gateway/start-gateway.js
```

### Start All Services
```bash
# Start all microservices
npm run microservices

# In separate terminals, start each service:
npm run start:auth-service
npm run start:hierarchy
npm run start:global-masters
npm run start:communication
npm run start:task-management
npm run start:zones-capacity
npm run start:survey
```

## 📡 Gateway Endpoints

### Health & Status
- `GET /gateway/health` - Gateway health check
- `GET /gateway/status` - Detailed status of all services
- `GET /gateway/services` - List all registered services
- `GET /gateway/metrics` - Gateway performance metrics

### API Routes
- `GET|POST|PUT|DELETE /api/*` - Proxied to appropriate service
- `POST /api/webhook/*` - Public webhook routes
- `GET|POST /api/v1/*` - Public API routes

## 🔧 Configuration

### Environment Variables
```bash
# Gateway Configuration
GATEWAY_PORT=8000

# Service Ports
AUTH_SERVICE_PORT=3006
HIERARCHY_PORT=3001
GLOBAL_PORT=3007
COMMUNICATION_PORT=3002
TASK_MANAGEMENT_PORT=3004
ZONES_CAPACITY_PORT=3003
SURVEY_PORT=3005

# Security
JWT_SECRET=your_jwt_secret
CORS_ORIGIN=http://localhost:3000

# Database
MONGODB_URI=mongodb://localhost:27017/ams
```

### Gateway Settings
Edit `gateway/config.js` to customize:

```javascript
const GATEWAY_CONFIG = {
  port: 8000,
  timeout: 30000, // 30 seconds
  retries: 3,
  healthCheckInterval: 30000, // 30 seconds
  loadBalancing: 'round-robin', // round-robin, least-connections, random
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000 // requests per window
  }
};
```

## 🔄 Load Balancing

The gateway supports multiple load balancing strategies:

### Round Robin (Default)
Distributes requests evenly across healthy instances.

### Least Connections
Routes to the instance with the fastest response time.

### Random
Randomly selects a healthy instance.

## 🏥 Health Checking

The gateway continuously monitors service health:

- **Health Check Interval**: 30 seconds
- **Timeout**: 5 seconds per check
- **Retry Logic**: 3 attempts with exponential backoff
- **Circuit Breaker**: Automatically routes around unhealthy services

## 📊 Monitoring

### Real-time Status
```bash
curl http://localhost:8000/gateway/status
```

### Service List
```bash
curl http://localhost:8000/gateway/services
```

### Performance Metrics
```bash
curl http://localhost:8000/gateway/metrics
```

## 🔒 Security Features

### Rate Limiting
- **Default**: 1000 requests per 15 minutes per IP
- **Bypass**: Health check endpoints are excluded
- **Headers**: Includes retry-after information

### Authentication
- **JWT Validation**: All `/api/*` routes require valid JWT
- **Public Routes**: `/api/v1/*` and `/api/webhook/*` are public
- **User Context**: Forwards user information to services

### Security Headers
- **Helmet**: Adds security headers
- **CORS**: Configurable cross-origin requests
- **Request ID**: Unique ID for request tracing

## 🚨 Error Handling

### Service Unavailable
```json
{
  "success": false,
  "message": "Service temporarily unavailable",
  "service": "hierarchy",
  "error": "Connection timeout"
}
```

### Route Not Found
```json
{
  "success": false,
  "message": "Route not found",
  "path": "/api/unknown",
  "method": "GET"
}
```

### Rate Limit Exceeded
```json
{
  "success": false,
  "message": "Too many requests from this IP, please try again later.",
  "retryAfter": 900
}
```

## 🔧 Development

### Adding New Services
1. Add service configuration to `gateway/config.js`
2. Update the `SERVICES` object with routes and health check path
3. Restart the gateway

### Custom Middleware
Add middleware in `gateway/middleware.js`:

```javascript
const customMiddleware = (req, res, next) => {
  // Your custom logic
  next();
};
```

### Debugging
Enable debug logging:
```bash
DEBUG=gateway:* npm run start:gateway
```

## 📈 Performance Tips

1. **Service Health**: Keep services healthy for better performance
2. **Connection Pooling**: Gateway reuses connections to services
3. **Compression**: Responses are automatically compressed
4. **Caching**: Consider adding Redis for response caching
5. **Load Balancing**: Use multiple instances of services for better performance

## 🛠️ Troubleshooting

### Gateway Won't Start
- Check if port 8000 is available
- Verify database connection
- Check environment variables

### Service Not Found
- Verify service is running on configured port
- Check service health endpoint
- Review gateway logs for routing issues

### High Response Times
- Check service health and response times
- Monitor gateway metrics
- Consider scaling services horizontally

## 🔄 Deployment

### Production Deployment
```bash
# Set production environment
NODE_ENV=production

# Start with PM2
pm2 start gateway/start-gateway.js --name "ams-gateway"

# Or with Docker
docker build -t ams-gateway .
docker run -p 8000:8000 ams-gateway
```

### Health Checks in Production
Set up monitoring for:
- Gateway health: `GET /gateway/health`
- Service status: `GET /gateway/status`
- Performance metrics: `GET /gateway/metrics`

The gateway is now ready to handle all your API requests and route them to the appropriate microservices! 🎉
