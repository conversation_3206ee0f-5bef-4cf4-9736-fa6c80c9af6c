const express = require("express");
const router = express.Router();

const healthcheckRoute = require("./healthcheck.route");
const dashboardRoute = require("./dashboard.route");
const inboxRoute = require("./inbox.route");
const composeRoute = require("./compose.route");
const emailReportRoute = require("./emailReport.route");
const emailLogRoute = require("./emailLog.route");
const meeting = require("./metting.route");

router.use("/communication/healthcheck", healthcheckRoute);
router.use("/communication/dashboard", dashboardRoute);
router.use("/communication/inbox", inboxRoute);
router.use("/communication/compose", composeRoute);
router.use("/communication/email-report", emailReportRoute);
router.use("/communication/email-logs", emailLogRoute);
router.use("/communication/meeting", meeting);

module.exports = router;
