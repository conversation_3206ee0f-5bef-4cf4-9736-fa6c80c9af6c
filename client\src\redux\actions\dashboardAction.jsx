import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';
import { CommunicationDashboardApiUrl, HierarchyDashboardApiUrl } from 'utils/constant';
const getDashboardURL = (endponints) => {
  return `${HierarchyDashboardApiUrl}${endponints}`;
};
const getCommunicationDashboardURL = (endponints) => {
  return `${CommunicationDashboardApiUrl}${endponints}`;
};

const getDashboard = (data, dispatch) => {
  return postRequest('hierarchy', getDashboardURL('/get/analytics'), data, true, dispatch);
};

const getCommmunicationDashboard = (data, dispatch) => {
  return postRequest(getCommunicationDashboardURL('get'), data, true, dispatch);
};

const clearAllCache = (data, dispatch) => {
  return getRequest('hierarchy', 'hierarchy/system-user/get/clearAllCache', data, true, dispatch);
};

export const getDashboardAction = createGenericAsyncThunk('dashboard/getDashboardAction', getDashboard, 'get');

export const getCommunicationDashboardAction = createGenericAsyncThunk(
  'dashboard/getCommunicationDashboardAction',
  getCommmunicationDashboard,
  'get'
);

export const clearCacheAction = createGenericAsyncThunk('clear/Cache', clearAllCache);
