const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
  DELETE_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { WaazVenueType, WaazVenue } = require("../../zonesCapacity/models");

const getAllWaazVenueTypes = apiHandler(async (req, res) => {
  const waazVenueTypes = await WaazVenueType.find().sort({ _id: -1 });
  return apiResponse(FETCH, "Waaz Venue Types", waazVenueTypes, res);
});

const addEditWaazVenueType = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = { uniqueName: data.uniqueName };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingWaazVenueType = await WaazVenueType.findOne(
    uniqueNameQueryObject
  );

  if (existingWaazVenueType) {
    return apiError(
      CUSTOM_ERROR,
      "Waaz Venue Type with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;

    const newWaazVenueType = new WaazVenueType(data);

    let savedWaazVenueTypeData = await newWaazVenueType.save();

    return apiResponse(
      ADD_SUCCESS,
      "Waaz Venue Type",
      savedWaazVenueTypeData,
      res
    );
  } else {
    data.updatedBy = req.user._id;

    let updatedWaazVenueTypeData = await WaazVenueType.findByIdAndUpdate(
      id,
      data,
      { new: true, runValidators: true }
    );

    if (!updatedWaazVenueTypeData) {
      return apiError(NOT_FOUND, "Waaz Venue Type", null, res);
    }

    return apiResponse(
      UPDATE_SUCCESS,
      "Waaz Venue Type",
      updatedWaazVenueTypeData,
      res
    );
  }
});

const getSingleWaazVenueType = apiHandler(async (req, res) => {
  const { id } = req.params;
  const waazVenueTypeData = await WaazVenueType.findById(id);

  if (!waazVenueTypeData)
    return apiError(NOT_FOUND, "Waaz Venue Type", null, res);

  return apiResponse(FETCH, "Waaz Venue Type", waazVenueTypeData, res);
});

const deleteWaazVenueType = apiHandler(async (req, res) => {
  const { id } = req.params;
  const isUsedInSeating = await WaazVenue.findOne({
    waazVenueTypeID: toObjectId(id),
  });
  if (isUsedInSeating) {
    return apiError(DELETE_ERROR, "Waaz Venue Type is used in Waaz Seating", null, res);
  }
  const waazVenueTypeData = await WaazVenueType.findOneAndDelete({ _id: id });

  if (!waazVenueTypeData)
    return apiError(NOT_FOUND, "Waaz Venue Type", null, res);

  return apiResponse(DELETE_SUCCESS, "Waaz Venue Type", null, res);
});

module.exports = {
  getAllWaazVenueTypes,
  addEditWaazVenueType,
  getSingleWaazVenueType,
  deleteWaazVenueType,
};
