import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';
import { HRKhidmatRequestApiUrl } from 'utils/constant';

const getKhidmatRequestURL = (endponints) => {
  return `${HRKhidmatRequestApiUrl}${endponints}`;
};

const getAllKGRequest = (data, dispatch) => {
  let { arazCityID, miqaatID, ...rest } = data || {};
  let payload = { ...rest };
  return postRequest('hierarchy', getKhidmatRequestURL(`get-all-v2/${arazCityID}/${miqaatID}`), payload, true, dispatch);
};

const getAllKGFileRequest = (data, dispatch) => {
  let { arazCityID, miqaatID, ...rest } = data || {};
  let payload = { ...rest };
  return postRequest('hierarchy', getKhidmatRequestURL(`get-all-v2/${arazCityID}/${miqaatID}`), payload, true, dispatch);
};

const checkKGReqStatus = (data, dispatch) => {
  return postRequest('hierarchy', getKhidmatRequestURL(`get/interest-status`), data, true, dispatch);
};

const getKhidmatRequest = (data, dispatch) => {
  return getRequest('hierarchy', getKhidmatRequestURL(`get/${data}`), data, true, dispatch);
};

const addKhidmatRequest = (data, dispatch) => {
  return postRequest('hierarchy', getKhidmatRequestURL('add'), data, true, dispatch);
};

const getITSDetailByIDsKGpool = (data, dispatch) => {
  return postRequest('hierarchy', getKhidmatRequestURL('add/import-its-users'), data, true, dispatch);
};

const registerKGUser = (data, dispatch) => {
  return postRequest('hierarchy', getKhidmatRequestURL('add/assign'), data, true, dispatch);
};

export const checkKGRequestStatusAction = createGenericAsyncThunk('HR/checkKGRequestStatusAction', checkKGReqStatus, 'get');

export const getAllKGRequestAction = createGenericAsyncThunk('HR/getAllKGRequestAction', getAllKGRequest, 'get');

export const getAllKGExportRequestAction = createGenericAsyncThunk('HR/getAllKGExportRequestAction', getAllKGFileRequest, 'get');

export const getKhidmatRequestAction = createGenericAsyncThunk('HR/getKhidmatRequestAction', getKhidmatRequest, 'get');

export const addKhidmatRequestAction = createGenericAsyncThunk('HR/addKhidmatRequestAction', addKhidmatRequest);

export const getKGDetailKGPoolAction = createGenericAsyncThunk('Role/getKGDetailKGPoolAction', getITSDetailByIDsKGpool, 'get');

export const registerKGUserAction = createGenericAsyncThunk('Role/registerKGUserAction', registerKGUser);
