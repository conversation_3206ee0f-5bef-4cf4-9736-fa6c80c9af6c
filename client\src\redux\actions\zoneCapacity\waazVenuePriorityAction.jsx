import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityWaazVenuePriorityApiUrl } from 'utils/constant';

const getWaazVenuePriorityURL = (endponints) => {
  return `${ZoneCapacityWaazVenuePriorityApiUrl}${endponints}`;
};

const getAllWaazVenuePriority = (data, dispatch) => {
  return getRequest('global', getWaazVenuePriorityURL('get'), data, true, dispatch);
};

const getSingleWaazVenuePriority = (data, dispatch) => {
  return getRequest('global', getWaazVenuePriorityURL(`get/${data}`), data, true, dispatch);
};

const addWaazVenuePriority = (data, dispatch) => {
  return postRequest('global', getWaazVenuePriorityURL('add'), data, true, dispatch);
};

const updateWaazVenuePriority = (data, dispatch) => {
  return putRequest('global', getWaazVenuePriorityURL('edit'), data, true, dispatch);
};

const deleteWaazVenuePriority = (data, dispatch) => {
  return deleletRequest('global', getWaazVenuePriorityURL(`delete/${data}`), data, true, dispatch);
};

export const getAllWaazVenuePriorityAction = createGenericAsyncThunk(
  'waazVenuePriority/getAllWaazVenuePriorityAction',
  getAllWaazVenuePriority,
  'get'
);

export const getSingleWaazVenuePriorityAction = createGenericAsyncThunk(
  'waazVenuePriority/getSingleWaazVenuePriorityAction',
  getSingleWaazVenuePriority,
  'get'
);

export const addWaazVenuePriorityAction = createGenericAsyncThunk('waazVenuePriority/addWaazVenuePriorityAction', addWaazVenuePriority);

export const updateWaazVenuePriorityAction = createGenericAsyncThunk(
  'waazVenuePriority/updateWaazVenuePriorityAction',
  updateWaazVenuePriority
);

export const deleteWaazVenuePriorityAction = createGenericAsyncThunk(
  'waazVenuePriority/deleteWaazVenuePriorityAction',
  deleteWaazVenuePriority
);
