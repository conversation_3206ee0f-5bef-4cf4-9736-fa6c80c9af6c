import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityZoneApiUrl, HierarchyZoneReportApiUrl, ZoneCapacityZoneFileApiUrl, HierarchyDepReportApiUrl } from 'utils/constant';
import { getArazCityURL } from './arazCityAction';
const getZoneURL = (endponints) => {
  return `${ZoneCapacityZoneApiUrl}${endponints}`;
};

const getArazCityZone = (data, dispatch) => {
  return getRequest('zonesCapacity', getZoneURL('get'), data, true, dispatch);
};

const getZoneyByArazCity = (data, dispatch) => {
  return getRequest('zonesCapacity', getZoneURL(`get/by-araz-city/${data}`), data, true, dispatch);
};

const getSingleZone = (data, dispatch) => {
  return getRequest('zonesCapacity', getZoneURL(`get/${data}`), data, true, dispatch);
};

const zoneWiseReport = (data, dispatch) => {
  const { kgTypes, ...rest } = data;
  const payload = kgTypes && kgTypes?.length > 0 ? data : rest;
  return postRequest('hierarchy', HierarchyZoneReportApiUrl, payload, true, dispatch);
};

const viewZoneReport = (data, dispatch) => {
  return getRequest('zonesCapacity', getZoneURL(`get/${data}`), data, true, dispatch);
};

const getZoneDepartmentTeamReport = (data, dispatch) => {
  return postRequest('hierarchy', 'hierarchy/araz-city-zone-team-report/get', data, true, dispatch);
};

const zoneHODReport = (data, dispatch) => {
  const { kgTypes, ...rest } = data;
  const payload = kgTypes && kgTypes?.length > 0 ? data : rest;
  return postRequest('hierarchy', `${HierarchyZoneReportApiUrl}/department-wise-zonelead-report`, payload, true, dispatch);
};

const addArazCityZone = (data, dispatch) => {
  return postRequest('zonesCapacity', getZoneURL('add'), data, true, dispatch);
};

const updateArazCityZone = (data, dispatch) => {
  return putRequest('zonesCapacity', getZoneURL('edit'), data, true, dispatch);
};

const deleteArazCityZone = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getZoneURL('delete'), data, true, dispatch);
};

const getZoneDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', `${ZoneCapacityZoneFileApiUrl}/get/araz-city-zone-files`, data, true, dispatch);
};

const getZoneMasterDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getZoneURL('get/get-master-files'), data, true, dispatch);
};

const uploadZoneDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', `${ZoneCapacityZoneFileApiUrl}/upload/newFile`, data, true, dispatch, 'formdata');
};

const viewZoneDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', `${ZoneCapacityZoneFileApiUrl}/get/download-url`, data, true, dispatch);
};

const updateZoneDrwaingFileStatus = (data, dispatch) => {
  return putRequest('zonesCapacity', `${ZoneCapacityZoneFileApiUrl}/approve`, data, true, dispatch);
};

const deleteZoneDrawingFile = (data, dispatch) => {
  return deleletRequest('zonesCapacity', `${ZoneCapacityZoneFileApiUrl}/upload/delete/${data}`, data, true, dispatch);
};

export const getArazCityZoneAction = createGenericAsyncThunk('arazCityZone/getArazCityZoneAction', getArazCityZone, 'get');

export const getZoneyByArazCityAction = createGenericAsyncThunk('arazCityZone/getZoneyByArazCityAction', getZoneyByArazCity, 'get');

export const getSingleArazCityZoneAction = createGenericAsyncThunk('arazCityZone/getSingleArazCityZoneAction', getSingleZone, 'get');

export const addArazCityZoneAction = createGenericAsyncThunk('arazCityZone/addArazCityZoneAction', addArazCityZone);

export const updateArazCityZoneAction = createGenericAsyncThunk('arazCityZone/updateArazCityZoneAction', updateArazCityZone);

export const deleteArazCityZoneAction = createGenericAsyncThunk('arazCityZone/deleteArazCityZoneAction', deleteArazCityZone);

export const zoneWiseReportAction = createGenericAsyncThunk('arazCityZone/zoneWiseReportAction', zoneWiseReport, 'get');

export const getZoneDepartmentTeamReportAction = createGenericAsyncThunk(
  'arazCityZone/zoneDepartmentTeamReport',
  getZoneDepartmentTeamReport,
  'get'
);

export const viewZoneReportAction = createGenericAsyncThunk('arazCityZone/viewZoneReportAction', viewZoneReport, 'get');

//Drawing Files

export const getZoneDrawingFileAction = createGenericAsyncThunk('arazCityZone/getDrawingFileAction', getZoneDrawingFile);

export const getZoneMasterDrawingFileAction = createGenericAsyncThunk(
  'arazCityZone/getDrawingMasterFileAction',
  getZoneMasterDrawingFile,
  'get'
);

export const uploadZoneDrawingFileAction = createGenericAsyncThunk('arazCityZone/uploadZoneDrawingFileAction', uploadZoneDrawingFile);

export const viewZoneDrawingileAction = createGenericAsyncThunk('arazCityZone/viewZoneDrawingFile', viewZoneDrawingFile, 'get');

export const updateZoneDrwaingFileStatusAction = createGenericAsyncThunk(
  'arazCityZone/updateZoneDrwaingFileStatusAction',
  updateZoneDrwaingFileStatus
);

export const deleteZoneDrwaingFileAction = createGenericAsyncThunk('arazCityZone/deleteDrwaingFileAction', deleteZoneDrawingFile);
export const zoneHODReportAction = createGenericAsyncThunk('arazCityZone/zoneHODReportAction', zoneHODReport, 'get');
