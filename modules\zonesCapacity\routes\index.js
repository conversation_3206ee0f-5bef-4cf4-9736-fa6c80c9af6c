const express = require("express");
const router = express.Router();

const healthcheckRoute = require("./healthcheck.route");
const waazVenueRoute = require("./waazVenue.route");
const globalFunction = require("./globalFunction.route");
const zoneMapping = require("./zoneMapping.route");
const mawaidVenueRoute = require("./mawaidVenue.route");
const dashboard = require("./dashboard.route");
const arazCityZoneRoute = require("../../hierarchy/routes/arazCityZone.route"); 
const razaMappingRoute = require("./razaMapping.route")

router.use("/zones-capacity/healthcheck", healthcheckRoute);
router.use("/zones-capacity/waaz-venue", waazVenueRoute);
router.use("/zones-capacity/mawaid-venue", mawaidVenueRoute);
router.use("/zones-capacity/global-function", globalFunction);
router.use("/zones-capacity/zone-mapping", zoneMapping);
router.use("/zones-capacity/dashboard", dashboard);
router.use("/zones-capacity/araz-city-zone", arazCityZoneRoute);
router.use("/zones-capacity/raza-mapping", razaMappingRoute);

module.exports = router;
