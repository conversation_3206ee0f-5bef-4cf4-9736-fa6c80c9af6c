import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyKgRequisitionApiUrl } from 'utils/constant';
import { getUserApiURL } from './profileAction';

const getKGReqisitionURL = (endponints) => {
  return `${HierarchyKgRequisitionApiUrl}${endponints}`;
};

const getAllKGRequisition = (data, dispatch) => {
  return getRequest(
    'hierarchy',
    getKGReqisitionURL(`get/requisitions?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`),
    data,
    true,
    dispatch
  );
};

const getAllKGRequisitionAppliactions = (data, dispatch) => {
  return getRequest(
    'hierarchy',
    `hierarchy/kg-requisition-application/get/requisitions?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`,
    data,
    true,
    dispatch
  );
};
const getAllKGRequisitionAppliactionsWithoutArazCityMiqaat = (data, dispatch) => {
  return getRequest('hierarchy', `hierarchy/kg-requisition-application/get/requisitions`, data, true, dispatch);
};

const getSingleKGRequisition = (data, dispatch) => {
  return getRequest('hierarchy', getKGReqisitionURL(`get/requisitions/${data}`), data, true, dispatch);
};

const addKGRequisition = (data, dispatch) => {
  return postRequest('hierarchy', getKGReqisitionURL(`add/requisitions`), data, true, dispatch);
};

const updateKgRequisition = (data, dispatch) => {
  return putRequest('hierarchy', getKGReqisitionURL(`edit/requisitions`), data, true, dispatch);
};

const deleteKgRequisition = (data, dispatch) => {
  return deleletRequest('hierarchy', getKGReqisitionURL(`delete/requisitions/${data}`), data, true, dispatch);
};
const assignKgRequisition = (data, dispatch) => {
  return patchRequest('hierarchy', getKGReqisitionURL(`edit/requisitions/assign`), data, true, dispatch);
};
const applyKgRequisition = (data, dispatch) => {
  return postRequest('hierarchy', 'hierarchy/kg-requisition-application/get/requisitions/apply', data, true, dispatch);
};

const deleteKG = (data, dispatch) => {
  return deleletRequest('hierarchy', 'hierarchy/kg-user/delete/bulk', data, true, dispatch);
};

export const getAllKGRequisitionAction = createGenericAsyncThunk('KG/getAllKGRequisition', getAllKGRequisition, 'get');
export const getAllKGRequisitionAppliactionsWithoutArazCityMiqaatAction = createGenericAsyncThunk(
  'KG/getAllKGRequisitionAppliactionsWithoutArazCityMiqaat',
  getAllKGRequisitionAppliactionsWithoutArazCityMiqaat,
  'get'
);
export const getAllKGRequisitionAppliactionsAction = createGenericAsyncThunk(
  'KG/getAllKGRequisitionAppliactions',
  getAllKGRequisitionAppliactions,
  'get'
);
export const getSingleKGRequisitionAction = createGenericAsyncThunk('KG/getSingleKGRequisition', getSingleKGRequisition, 'get');
export const addKGRequisitionAction = createGenericAsyncThunk('KG/addKGRequisition', addKGRequisition);
export const applyKgRequisitionAction = createGenericAsyncThunk('KG/applyKgRequisition', applyKgRequisition);
export const assignKgRequisitionAction = createGenericAsyncThunk('KG/assignKgRequisition', assignKgRequisition);
export const deleteKgRequisitionAction = createGenericAsyncThunk('KG/deleteKgRequisition', deleteKgRequisition);
export const updateKgRequisitionAction = createGenericAsyncThunk('KG/updateKgRequisition', updateKgRequisition);
export const bulkDeleteKGAction = createGenericAsyncThunk('KG/bulkDeleteKG', deleteKG);
