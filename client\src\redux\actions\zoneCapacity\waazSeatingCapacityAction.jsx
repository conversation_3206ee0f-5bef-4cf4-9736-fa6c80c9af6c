import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityWaazSeatingCapacityApiUrl } from 'utils/constant';

const getWaazSeatingCapacityURL = (endponints) => {
  return `${ZoneCapacityWaazSeatingCapacityApiUrl}${endponints}`;
};

const getAllWaazSeatingCapacity = (data, dispatch) => {
  return getRequest('global', getWaazSeatingCapacityURL('get'), data, true, dispatch);
};

const getSingleWaazSeatingCapacity = (data, dispatch) => {
  return getRequest('global', getWaazSeatingCapacityURL(`get/${data}`), data, true, dispatch);
};

const addWaazSeatingCapacity = (data, dispatch) => {
  return postRequest('global', getWaazSeatingCapacityURL('add'), data, true, dispatch);
};

const updateWaazSeatingCapacity = (data, dispatch) => {
  return putRequest('global', getWaazSeatingCapacityURL('edit'), data, true, dispatch);
};

const deleteWaazSeatingCapacity = (data, dispatch) => {
  return deleletRequest('global', getWaazSeatingCapacityURL(`delete/${data}`), data, true, dispatch);
};

export const getAllWaazSeatingCapacityAction = createGenericAsyncThunk(
  'waazSeatingCapacity/getAllWaazSeatingCapacityAction',
  getAllWaazSeatingCapacity,
  'get'
);

export const getSingleWaazSeatingCapacityAction = createGenericAsyncThunk(
  'waazSeatingCapacity/getSingleWaazSeatingCapacityAction',
  getSingleWaazSeatingCapacity,
  'get'
);

export const addWaazSeatingCapacityAction = createGenericAsyncThunk(
  'waazSeatingCapacity/addWaazSeatingCapacityAction',
  addWaazSeatingCapacity
);

export const updateWaazSeatingCapacityAction = createGenericAsyncThunk(
  'waazSeatingCapacity/updateWaazSeatingCapacityAction',
  updateWaazSeatingCapacity
);

export const deleteWaazSeatingCapacityAction = createGenericAsyncThunk(
  'waazSeatingCapacity/deleteWaazSeatingCapacityAction',
  deleteWaazSeatingCapacity
);
