const Joi = require("joi");
const {
  stringValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addWaazVenueSuitabilitySchema = Joi.object({
  name: stringValidation,
});

const getSingleWaazVenueSuitabilitySchema = Joi.object({
  id: idValidation,
});

const editWaazVenueSuitabilitySchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteWaazVenueSuitabilitySchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addWaazVenueSuitabilitySchema,
  getSingleWaazVenueSuitabilitySchema,
  editWaazVenueSuitabilitySchema,
  deleteWaazVenueSuitabilitySchema,
};
