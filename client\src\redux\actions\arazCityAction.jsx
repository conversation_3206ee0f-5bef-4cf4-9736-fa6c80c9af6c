import { get } from 'lodash';
import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyArazCityApiUrl, GetAllJamiatJamaatApiUrl, hofDataUrl, AccomodationArazCityReportApiUrl } from 'utils/constant';
export const getArazCityURL = (endponints) => {
  return `${HierarchyArazCityApiUrl}${endponints}`;
};

const getArazCity = (data, dispatch) => {
  return getRequest('global', getArazCityURL('get'), data, true, dispatch);
};

const getAllArazCity = (data, dispatch) => {
  return getRequest('global', getArazCityURL(`get?status=${data}`), data, true, dispatch);
};

const getArazCityReport = (data, dispatch) => {
  return postRequest('global', AccomodationArazCityReportApiUrl, data, true, dispatch);
};

const getAllJamiaats = (data, dispatch) => {
  return getRequest('hierarchy', GetAllJamiatJamaatApiUrl, data, true, dispatch);
};

const getAllJamaats = (data, dispatch) => {
  return postRequest('hierarchy', GetAllJamiatJamaatApiUrl, data, true, dispatch);
};

const getHofData = (data, dispatch) => {
  return postRequest('zonesCapacity', hofDataUrl, data, true, dispatch);
};

const getSingleArazCity = (data, dispatch) => {
  return getRequest('global', getArazCityURL(`get/${data}`), data, true, dispatch);
};

const getAllTimeZones = (data, dispatch) => {
  return getRequest('global', 'hierarchy/araz-city/get/timezone', data, true, dispatch);
};

const getArazCityByMiqaat = (data, dispatch) => {
  return getRequest('global', getArazCityURL(`get/by-miqaat/${data}`), data, true, dispatch);
};

const addArazCity = (data, dispatch) => {
  return postRequest('global', getArazCityURL('add'), data, true, dispatch, 'formdata');
};

const updateArazCity = (data, dispatch) => {
  return putRequest('global', getArazCityURL('edit'), data, true, dispatch, 'formdata');
};

const deleteArazCity = (data, dispatch) => {
  return deleletRequest('global', getArazCityURL(`delete/${data}`), data, true, dispatch);
};

export const getArazCityAction = createGenericAsyncThunk('arazcity/getArazCityAction', getArazCity, 'get');

export const getAllArazCityAction = createGenericAsyncThunk('arazcity/getArazCityAction', getAllArazCity, 'get');

export const getArazCityByMiqaatAction = createGenericAsyncThunk('arazcity/getArazCityByMiqaatAction', getArazCityByMiqaat, 'get');

export const getSingleArazCityAction = createGenericAsyncThunk('arazcity/getSingleArazCityAction', getSingleArazCity, 'get');

export const addArazCityAction = createGenericAsyncThunk('arazcity/addArazCityAction', addArazCity);

export const updateArazCityAction = createGenericAsyncThunk('arazcity/updateArazCityAction', updateArazCity);

export const deleteArazCityAction = createGenericAsyncThunk('arazcity/deleteArazCityAction', deleteArazCity);

export const getAllJamiatsAction = createGenericAsyncThunk('jamiat/getAllJamiatsAction', getAllJamiaats, 'get', false);

export const getAllJamaatsAction = createGenericAsyncThunk('jamaat/getAllJamaatsAction', getAllJamaats, 'get', false);

export const getArazCityReportAction = createGenericAsyncThunk('arazCity/getArazCityReportAction', getArazCityReport, 'get', false);

export const getHofDataAction = createGenericAsyncThunk('arazCity/getHofDataAction', getHofData, 'get', false);

export const getTimeZoneAction = createGenericAsyncThunk('arazCity/getTimeZoneAction', getAllTimeZones, 'get', false);
