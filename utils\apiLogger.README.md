# API Logging System

This document describes the comprehensive asynchronous API logging system implemented in the AMS project.

## Overview

The API logging system captures detailed information about every API call made to your services, including:
- Request details (method, endpoint, headers, body, query parameters)
- Response details (status, headers, body)
- User information (extracted from JWT tokens)
- Timing information (request time, response time, duration)
- Error information (if any)
- Additional metadata (IP address, user agent, service name)

All logging is done **asynchronously** using BullMQ queues to ensure it doesn't affect API response times.

## Features

### 🚀 Asynchronous Processing
- Uses BullMQ for queue-based processing
- Doesn't block API responses
- Configurable concurrency and retry logic

### 🔐 JWT User Extraction
- Automatically extracts user information from JWT tokens
- Captures user ID, ITS ID, name, and system user status
- Handles invalid/expired tokens gracefully

### 🛡️ Data Sanitization
- Automatically redacts sensitive information (passwords, tokens, etc.)
- Configurable sensitive field detection
- Preserves data structure while protecting privacy

### 📊 Comprehensive Logging
- Request/response headers and bodies
- Query parameters and route parameters
- Timing information with millisecond precision
- Error stack traces for debugging
- Service identification for microservices

### 🗄️ Optimized Storage
- MongoDB with optimized indexes for fast queries
- TTL (Time To Live) for automatic log cleanup (90 days default)
- Efficient data structure for storage and retrieval

## Architecture

```
API Request → Middleware → Queue → Worker → MongoDB
     ↓
API Response (immediate)
```

### Components

1. **ApiLog Model** (`modules/globalMasters/models/apiLog.model.js`)
   - MongoDB schema for storing API logs
   - Optimized indexes for performance
   - TTL for automatic cleanup

2. **API Logger Utility** (`utils/apiLogger.util.js`)
   - JWT token extraction
   - Data sanitization
   - Log data formatting

3. **API Logger Middleware** (`middlewares/apiLogger.middleware.js`)
   - Express middleware for intercepting requests/responses
   - Asynchronous log queuing

4. **API Log Queue** (`utils/queues/apiLogQueue.js`)
   - BullMQ queue and worker setup
   - Retry logic and error handling

## Usage

### Automatic Integration

The logging system is automatically integrated into all service files:
- `app.js` (main gateway)
- `services/auth.service.js`
- `services/global/global.service.js`
- `services/hierarchy.service.js`
- `services/taskManagement.service.js`
- And other service files...

### Manual Integration

If you need to add logging to a new service:

```javascript
const { apiLoggerMiddleware, apiErrorLoggerMiddleware, attachStartTime } = require("../middlewares/apiLogger.middleware");

// Add early in middleware chain
app.use(attachStartTime);
app.use(apiLoggerMiddleware);

// Your other middlewares and routes...

// Add error logging at the end
app.use(apiErrorLoggerMiddleware);
```

### Configuration

#### Skip Logging for Specific Endpoints

Edit `middlewares/apiLogger.middleware.js`:

```javascript
const skipLogging = [
  '/api/healthcheck',
  '/api/health',
  '/favicon.ico',
  '/api/ping',
  '/your-endpoint-to-skip'
];
```

#### Customize Sensitive Fields

Edit `utils/apiLogger.util.js`:

```javascript
const sensitiveFields = [
  'password', 'token', 'authorization', 'cookie', 'session',
  'secret', 'key', 'private', 'confidential', 'auth',
  'your-custom-sensitive-field'
];
```

#### Adjust TTL (Log Retention)

Edit `modules/globalMasters/models/apiLog.model.js`:

```javascript
// TTL index - change expireAfterSeconds value
apiLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 }); // 90 days
```

## Querying Logs

### Find logs by user
```javascript
const logs = await ApiLog.find({ userId: "user_id_here" }).sort({ requestTime: -1 });
```

### Find logs by endpoint
```javascript
const logs = await ApiLog.find({ endpoint: "/api/user/login" }).sort({ requestTime: -1 });
```

### Find failed requests
```javascript
const logs = await ApiLog.find({ success: false }).sort({ requestTime: -1 });
```

### Find slow requests
```javascript
const logs = await ApiLog.find({ duration: { $gt: 5000 } }).sort({ duration: -1 });
```

### Find logs by date range
```javascript
const logs = await ApiLog.find({
  requestTime: {
    $gte: new Date('2024-01-01'),
    $lte: new Date('2024-01-31')
  }
}).sort({ requestTime: -1 });
```

## Performance Considerations

### Queue Configuration
- **Concurrency**: 5 workers process logs simultaneously
- **Retry Logic**: Failed jobs retry up to 3 times with exponential backoff
- **Cleanup**: Keeps last 100 completed jobs and 50 failed jobs

### Database Indexes
- `requestTime`: For time-based queries
- `userId`: For user-specific queries
- `endpoint`: For endpoint-specific queries
- `success`: For filtering successful/failed requests
- `service`: For service-specific queries

### Memory Usage
- Response data is captured but sanitized
- Large responses are stored as-is (consider implementing size limits if needed)
- TTL ensures old logs are automatically cleaned up

## Monitoring

### Queue Health
```javascript
const { apiLogQueue } = require('./utils/queues/apiLogQueue');

// Get queue stats
const waiting = await apiLogQueue.getWaiting();
const active = await apiLogQueue.getActive();
const completed = await apiLogQueue.getCompleted();
const failed = await apiLogQueue.getFailed();
```

### Worker Status
Check console logs for worker status:
- `API log saved: GET /api/user/profile - 200`
- `API log job 123 completed successfully`
- `API log job 456 failed: Error message`

## Troubleshooting

### Common Issues

1. **Logs not appearing**
   - Check Redis connection
   - Verify queue worker is running
   - Check console for error messages

2. **High memory usage**
   - Reduce TTL for faster cleanup
   - Implement response size limits
   - Monitor queue size

3. **Slow API responses**
   - Verify logging is truly asynchronous
   - Check if middleware is placed correctly
   - Monitor queue processing speed

### Debug Mode

Enable debug logging by setting environment variable:
```bash
DEBUG=bullmq:* npm start
```

## Security Considerations

- Sensitive data is automatically redacted
- JWT tokens are not stored in logs
- IP addresses are logged for security auditing
- Consider implementing log access controls
- Regular log cleanup prevents data accumulation

## Future Enhancements

- Dashboard for log visualization
- Real-time log streaming
- Advanced filtering and search
- Log aggregation and analytics
- Integration with monitoring tools (Grafana, ELK stack)
- Rate limiting based on log patterns
- Automated alerting for error patterns
