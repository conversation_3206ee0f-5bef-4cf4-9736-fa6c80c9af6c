import { createGenericAsyncThunk } from 'redux/helper';
import { postRequest } from 'utils/axios';
import { HierarchyKGApiUrl } from 'utils/constant';

const getAssignPositionURL = (endponints) => {
  return `${HierarchyKGApiUrl}${endponints}`;
};

const getITSDetailByIDs = (data, dispatch) => {
  return postRequest('hierarchy', getAssignPositionURL('get/import-its-users'), data, true, dispatch);
};

const assignPosition = (data, dispatch) => {
  return postRequest('hierarchy', getAssignPositionURL('add/assign'), data, true, dispatch);
};

const assignKGUser = (data, dispatch) => {
  return postRequest('hierarchy', getAssignPositionURL('add/upload-users'), data, true, dispatch);
};

export const assignPositionAction = createGenericAsyncThunk('Role/addRoleByITSAction', assignPosition, 'get');

export const assignKGUserAction = createGenericAsyncThunk('Role/addRoleByITSAction', assignKGUser, 'get');

export const getKGDetailByITSIDsAction = createGenericAsyncThunk('Role/getKGDetailByITSIDsAction', getITSDetailByIDs, 'get');
