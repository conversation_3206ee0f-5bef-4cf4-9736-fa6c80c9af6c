const {
  api<PERSON><PERSON><PERSON>,
  api<PERSON><PERSON>r,
  apiResponse,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { HierarchyPosition, ArazCity } = require("../../hierarchy/models");
const {
  isEmpty,
  getUniqueName,
  toObjectId,
} = require("../../../utils/misc.util");
const {
  generateHierarchy,
} = require("../../hierarchy/controllers/hierarchy.controller");
const {
  upsertPositionAssignment,
} = require("../../hierarchy/controllers/positionAssignment.controller");
const {
  EventActions,
  addEventLog,
  Modules,
} = require("../../../utils/eventLogs.util");
const constants = require("../../../constants");

const getAllHierarchyPositions = apiHandler(async (req, res) => {
  const { isSystemUser } = req.user;
  const findQuery = isSystemUser ? {} : { isVisibleForArazCityUser: true };

  const hierarchyPositions = await HierarchyPosition.find(findQuery)
    .sort({ _id: -1 })
    .populate("parents", "name");

  return apiResponse(FETCH, "Hierarchy Positions", hierarchyPositions, res);
});

const addHierarchyPosition = apiHandler(async (req, res) => {
  const {
    name,
    alias,
    isZonal,
    isDepartmental,
    isVisibleForArazCityUser,
    parentType,
    parents,
    weightage,
    countRecommendation,
  } = req.body;

  const uniqueName = getUniqueName(name);

  const existingHierarchyPosition = await HierarchyPosition.findOne({
    $or: [{ uniqueName }, alias ? { alias } : null].filter(Boolean),
  });

  if (!isEmpty(existingHierarchyPosition)) {
    return apiError(
      CUSTOM_ERROR,
      "Hierarchy Position with this name already exists",
      null,
      res
    );
  }

  const newHierarchyPosition = new HierarchyPosition({
    name,
    alias,
    uniqueName,
    isZonal,
    isDepartmental,
    isVisibleForArazCityUser,
    parentType,
    parents,
    weightage,
    countRecommendation,
    createdBy: req.user._id,
  });

  const newHierarchyPositionData = await newHierarchyPosition.save();

  await ArazCity.updateMany(
    {},
    {
      $push: {
        hierarchyPositions: {
          hierarchyPositionID: newHierarchyPositionData._id,
          countRecommendation: newHierarchyPositionData.countRecommendation,
          weightage: newHierarchyPositionData.weightage,
        },
      },
    }
  );

  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "Hierarchy Position",
    req.user._id
  );

  apiResponse(ADD_SUCCESS, "Hierarchy Position", newHierarchyPositionData, res);

  const arazCityData = await ArazCity.find({}, "miqaatID").lean();

  await Promise.all(
    arazCityData.map(async (city) => {
      await generateHierarchy(city.miqaatID, city._id);
    })
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Hierarchy Position",
    constants.AMS_SYSTEMID
  );
});

const getSingleHierarchyPosition = apiHandler(async (req, res) => {
  const { id } = req.params;
  const { isSystemUser } = req.user;
  const findQuery = isSystemUser
    ? { _id: id }
    : { _id: id, isVisibleForArazCityUser: true };

  const hierarchyPositionData = await HierarchyPosition.findOne(
    findQuery
  ).populate("parents", "name");

  if (isEmpty(hierarchyPositionData)) {
    return apiError(NOT_FOUND, "Hierarchy Position", null, res);
  }
  return apiResponse(FETCH, "Hierarchy Position", hierarchyPositionData, res);
});

const editHierarchyPosition = apiHandler(async (req, res) => {
  const {
    id,
    name,
    alias,
    weightage,
    countRecommendation,
    isVisibleForArazCityUser,
  } = req.body;

  const isSystemHierarchyPosition = await HierarchyPosition.findOne({
    _id: id,
  }).select("isSystem");

  if (isEmpty(isSystemHierarchyPosition)) {
    return apiError(NOT_FOUND, "Hierarchy Position", null, res);
  }

  let updateData = {
    updatedBy: req.user._id,
    weightage,
    countRecommendation,
    isVisibleForArazCityUser,
  };

  // if (!isSystemHierarchyPosition?.isSystem) {
  if (isSystemHierarchyPosition) {
    const uniqueName = getUniqueName(name);

    const existingHierarchyPosition = await HierarchyPosition.findOne({
      $or: [{ uniqueName }, alias ? { alias } : null].filter(Boolean),
      _id: { $ne: id },
    });

    if (!isEmpty(existingHierarchyPosition)) {
      return apiError(
        CUSTOM_ERROR,
        "Hierarchy Position with this name already exists",
        null,
        res
      );
    }
  }

  const hierarchyPositionData = await HierarchyPosition.findByIdAndUpdate(
    id,
    updateData,
    {
      new: true,
      runValidators: true,
    }
  );

  // await ArazCity.updateMany(
  //   { "hierarchyPositions.hierarchyPositionID": hierarchyPositionData._id },
  //   {
  //     $set: {
  //       "hierarchyPositions.$.weightage": hierarchyPositionData.weightage,
  //       "hierarchyPositions.$.countRecommendation":
  //         hierarchyPositionData.countRecommendation,
  //     },
  //   }
  // );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy Position",
    req.user._id
  );
  return apiResponse(
    UPDATE_SUCCESS,
    "Hierarchy Position",
    hierarchyPositionData,
    res
  );

  // const arazCityData = await ArazCity.find({}, "miqaatID").lean();

  // await Promise.all(
  //   arazCityData.map(async (city) => {
  //     await generateHierarchy(city.miqaatID, city._id);
  //   })
  // );
});

const deleteHierarchyPosition = apiHandler(async (req, res) => {
  const { id } = req.params;

  const hierarchyPositionData = await HierarchyPosition.findOneAndDelete({
    _id: id,
  });

  if (isEmpty(hierarchyPositionData)) {
    return apiError(NOT_FOUND, "Hierarchy Position", null, res);
  }

  await ArazCity.updateMany(
    { "hierarchyPositions.hierarchyPositionID": id },
    {
      $pull: { hierarchyPositions: { hierarchyPositionID: id } },
    }
  );
  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "Hierarchy Position",
    req.user._id
  );
  apiResponse(DELETE_SUCCESS, "Hierarchy Position", null, res);

  const arazCityData = await ArazCity.find({}, "miqaatID").lean();

  await Promise.all(
    arazCityData.map(async (city) => {
      await generateHierarchy(city.miqaatID, city._id);
    })
  );
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Hierarchy Position",
    constants.AMS_SYSTEMID
  );
});

const saveWeightageAndCount = apiHandler(async (req, res) => {
  const {
    arazCityID,
    hierarchyPositionID,
    arazCityZoneID,
    departmentID,
    weightage,
    countRecommendation,
  } = req.body;

  const existingArazCity = await ArazCity.findById(toObjectId(arazCityID));

  if (isEmpty(existingArazCity)) {
    return apiError(NOT_FOUND, "Araz City not found", null, res);
  }

  // let arazCityData = await ArazCity.findOneAndUpdate(
  //   {
  //     _id: toObjectId(arazCityID),
  //     "hierarchyPositions.hierarchyPositionID": toObjectId(hierarchyPositionID),
  //   },
  //   {
  //     $set: {
  //       "hierarchyPositions.$.weightage": weightage,
  //       "hierarchyPositions.$.countRecommendation": countRecommendation,
  //     },
  //   },
  //   { new: true }
  // );

  // if (isEmpty(arazCityData)) {
  //   arazCityData = await ArazCity.findByIdAndUpdate(
  //     toObjectId(arazCityID),
  //     {
  //       $push: {
  //         hierarchyPositions: {
  //           hierarchyPositionID: toObjectId(hierarchyPositionID),
  //           weightage,
  //           countRecommendation,
  //         },
  //       },
  //     },
  //     { new: true }
  //   );
  // }

  const positionAssignmentData = req.body;
  positionAssignmentData["positionID"] = hierarchyPositionID;
  positionAssignmentData["miqaatID"] = existingArazCity.miqaatID;
  await upsertPositionAssignment(positionAssignmentData);

  apiResponse(UPDATE_SUCCESS, "Count and Weightage", existingArazCity, res);

  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "Hierarchy for Hierarchy Position",
    constants.AMS_SYSTEMID
  );

  await generateHierarchy(existingArazCity.miqaatID, arazCityID);
});

module.exports = {
  getAllHierarchyPositions,
  addHierarchyPosition,
  getSingleHierarchyPosition,
  editHierarchyPosition,
  deleteHierarchyPosition,
  saveWeightageAndCount,
};
