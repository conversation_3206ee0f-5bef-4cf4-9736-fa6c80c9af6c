const { validate } = require("../../../middlewares/validation.middleware");

const {
  getAllKGTypes,
  addKGType,
  getSingleKGType,
  editKGType,
  deleteKGType,
} = require("../controllers/kgType.controller");

const {
  addKGTypeSchema,
  getSingleKGTypeSchema,
  editKGTypeSchema,
  deleteKGTypeSchema,
} = require("../validations/kgType.validation");

const router = require("express").Router();

router.get("/get", getAllKGTypes);

router.post(
  "/add",
  validate(addKGTypeSchema, "body"),
  addKGType
);

router.get(
  "/get/:id",
  validate(getSingleKGTypeSchema, "params"),
  getSingleKGType
);

router.put(
  "/edit",
  validate(editKGTypeSchema, "body"),
  editKGType
);

router.delete(
  "/delete/:id",
  validate(deleteKGTypeSchema, "params"),
  deleteKGType
);

module.exports = router;

