const { required } = require("joi");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const hotelSchema = new Schema(
  {
    hotelName: {
      type: String,
      required: true,
      trim: true,
    },
    arazCityZoneID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCityZone",
      trim: true,
      required:false
    },
    waazVenueID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenue",
      required:false
    },
    arazCityID: {
      type: Schema.Types.ObjectId,
      ref: "ArazCity",
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
    },
    address: {
      type: String,
      trim: true,
      required: false,
    },
    latitude: {
      type: String,
      trim: true,
      required: false,
    },
    longitude: {
      type: String,
      trim: true,
      required: false,
    },
    googleMapLink: {
      type: String,
      trim: true,
      required: false,
    },
    images: [
      {
        type: String,
        trim: true,
        required: false,
      },
    ],
  },
  { timestamps: true }
);

const Hotel = mongoose.model("Hotel", hotelSchema);
module.exports = Hotel;
