import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';
import {
  ArazCityHRDashboardAction,
  ArazCityReportAction,
  WhiteListOneDashboardAPIUrl,
  WhiteListTwoDashboardAPIUrl,
  ArazCityArrivalReportAction
} from 'utils/constant';

const getArazCityHRReportApiUrl = (endPoints) => {
  return `${ArazCityHRDashboardAction}/${endPoints}`;
};

const getArrivalReportApiUrl = (endPoints) => {
  return `${ArazCityArrivalReportAction}/${endPoints}`;
};

const getWhiteListTwoReportApiUrl = (endPoints) => {
  return `${WhiteListTwoDashboardAPIUrl}/${endPoints}`;
};

const getArazCityReportAPIUrl = (endPoints) => {
  return `${ArazCityReportAction}/${endPoints}`;
};

const getWhiteListDashboardOne = (data, dispatch) => {
  return getRequest('global', WhiteListOneDashboardAPIUrl, data, true, dispatch);
};

const getWhiteListDashboardTwo = (data, dispatch) => {
  return postRequest('global', getWhiteListTwoReportApiUrl(`get/report`), data, true, dispatch);
};

const getArazCityHRDashboard = (data, dispatch) => {
  return postRequest('global', getArazCityHRReportApiUrl('get/report'), data, true, dispatch);
};

const getWhiteList = (data, dispatch) => {
  return getRequest('global', getArazCityHRReportApiUrl('get/compile-list'), data, true, dispatch);
};

const getCompileListForReport = (data, dispatch) => {
  return getRequest('global', getWhiteListTwoReportApiUrl('get/compile-list'), data, true, dispatch);
};

const getDepartmentForWhiteList = (data, dispatch) => {
  return getRequest('global', getWhiteListTwoReportApiUrl('get/department'), data, true, dispatch);
};

const getReportArazCities = (data, dispatch) => {
  return getRequest('global', getArazCityHRReportApiUrl(`get/araz-city/${data}`), data, true, dispatch);
};

const saveArazCityHRDashboard = (data, dispatch) => {
  return postRequest('global', getArazCityHRReportApiUrl('add/report'), data, true, dispatch);
};

const getArazCityStats = (data, dispatch) => {
  return getRequest(
    'global',
    getArazCityReportAPIUrl(`get/venue-summary?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`),
    data,
    true,
    dispatch
  );
};

const getArrivalInfo = (data, dispatch) => {
  return getRequest('global', getArrivalReportApiUrl(`get?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`), data, true, dispatch);
  // return {
  //   data: {
  //     success: true,
  //     data: {
  //       modeOfTravelChart: [
  //         { name: 'Car', value: 45, color: '#FF6384' },
  //         { name: 'Bus', value: 25, color: '#FFCE56' },
  //         { name: 'Train', value: 15, color: '#36A2EB' },
  //         { name: 'Bicycle', value: 10, color: '#4BC0C0' },
  //         { name: 'Walking', value: 5, color: '#9966FF' }
  //       ],
  //       arrivalChart: [
  //         {
  //           date: '2025-06-01',
  //           day: 'Sunday',
  //           users: 20
  //         },
  //         {
  //           date: '2025-06-02',
  //           day: 'Monday',
  //           users: 23
  //         },
  //         {
  //           date: '2025-06-03',
  //           day: 'Tuesday',
  //           users: 0
  //         },
  //         {
  //           date: '2025-06-04',
  //           day: 'Wednesday',
  //           users: 0
  //         },
  //         {
  //           date: '2025-06-05',
  //           day: 'Thursday',
  //           users: 0
  //         },
  //         {
  //           date: '2025-06-06',
  //           day: 'Friday',
  //           users: 0
  //         },
  //         {
  //           date: '2025-06-07',
  //           day: 'Saturday',
  //           users: 0
  //         }
  //       ],
  //       arrivalInfo: []
  //     }
  //   }
  // };
};

const getUserReport = (data, dispatch) => {
  return getRequest(
    'global',
    getArazCityReportAPIUrl(`get/user-analytics?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`),
    data,
    true,
    dispatch
  );
};

const getKGPoolInterest = (data, dispatch) => {
  return getRequest(
    'global',
    getArazCityReportAPIUrl(`get/interest-position-overview?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`),
    data,
    true,
    dispatch
  );
};

const getKGPermissionWise = (data, dispatch) => {
  return getRequest(
    'global',
    getArazCityReportAPIUrl(`get/permission-wise-user?arazCityID=${data.arazCityID}&miqaatID=${data.miqaatID}`),
    data,
    true,
    dispatch
  );
};

const getArazCityZoneWise = (data, dispatch) => {
  return postRequest('global', 'report/araz-city-zone-wise/get/report', data, true, dispatch);
};

const getCompilelistID = (data, dispatch) => {
  return getRequest('global', getArazCityHRReportApiUrl('get/compile-list'), data, true, dispatch);
};

const getZoneWise = (data, dispatch) => {
  return postRequest('global', 'report/zone-wise/get/report', data, true, dispatch);
};

export const getWhiteListDashboardOneAction = createGenericAsyncThunk(
  'report/getWhiteListDashboardOneAction',
  getWhiteListDashboardOne,
  'get'
);

export const getWhiteListDashboardTwoAction = createGenericAsyncThunk(
  'report/getWhiteListDashboardTwoAction',
  getWhiteListDashboardTwo,
  'get'
);

export const getArazCityHRDashboardAction = createGenericAsyncThunk('report/getArazCityHRDashboardAction', getArazCityHRDashboard, 'get');

export const getWhiteListAction = createGenericAsyncThunk('report/getWhiteListAction', getWhiteList, 'get');

export const getCompileListForReportAction = createGenericAsyncThunk('report/getCompileListForReport', getCompileListForReport, 'get');

export const getReportArazCitiesAction = createGenericAsyncThunk('report/getReportArazCitiesAction', getReportArazCities, 'get');

export const getDepartmentForWhiteListAction = createGenericAsyncThunk(
  'report/getDepartmentForWhiteListAction',
  getDepartmentForWhiteList,
  'get'
);

export const saveArazCityHRDashboardAction = createGenericAsyncThunk('report/saveArazCityHRDashboardAction', saveArazCityHRDashboard);

export const getArazCityStatsAction = createGenericAsyncThunk('report/getArazCityStatsaction', getArazCityStats, 'get');

export const getArrivalInfoAction = createGenericAsyncThunk('report/getArrivalInfoAction', getArrivalInfo, 'get');

export const getUserReportAction = createGenericAsyncThunk('report/getUserReport', getUserReport, 'get');

export const getKGPoolInterestAction = createGenericAsyncThunk('report/getKGPoolInterest', getKGPoolInterest, 'get');

export const getKGPermissionWiseAction = createGenericAsyncThunk('report/getKGPermissionWise', getKGPermissionWise, 'get');

export const getArazCityZoneWiseAction = createGenericAsyncThunk('report/getArazCityZoneWiseAction', getArazCityZoneWise, 'get');

export const getCompilelistIDAction = createGenericAsyncThunk('report/getCompilelistIDAction', getCompilelistID, 'get');

export const getZoneWiseAction = createGenericAsyncThunk('report/getZoneWiseAction', getZoneWise, 'get');
