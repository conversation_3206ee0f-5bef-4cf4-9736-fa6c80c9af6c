const { validate } = require("../../../middlewares/validation.middleware");
const { updateDepartmentStatus, updateDepartmentKhidmatStatus } = require("../../globalMasters/controllers/department.controller");
const { updateDepartmentStatusSchema, updateDepartmentKhidmatStatusSchema } = require("../../globalMasters/validations/department.validation");

const router = require("express").Router();

router.patch(
  "/edit/update-status",
  validate(updateDepartmentStatusSchema, "body"),
  updateDepartmentStatus
);

router.patch(
  "/edit/update-khidmat-status",
  validate(updateDepartmentKhidmatStatusSchema, "body"),
  updateDepartmentKhidmatStatus
);

module.exports = router;
