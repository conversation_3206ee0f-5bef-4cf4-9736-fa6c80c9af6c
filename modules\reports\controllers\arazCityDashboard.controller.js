const {
  apiH<PERSON>ler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { FETCH, CUSTOM_ERROR } = require("../../../utils/message.util");
const { toObjectId } = require("../../../utils/misc.util");
const Hotel = require("../../muqimeenMehmaanMapping/models/hotel.model");
const { WaazVenue, MawaidVenue } = require("../../zonesCapacity/models");
const {
  KGUser,
  HierarchyPosition,
  ArazCity,
  KGType,
  SystemRole,
  Miqaat,
  ArazCityZone,
  Department,
  Interest,
} = require("../../hierarchy/models");
const dayjs = require("dayjs");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

// REPORT 1: Statistics based on ArazCity and Miqaat (existing functionality)
const getUsersSummaryReport = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.query;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const zonesCount = await ArazCityZone.countDocuments({
    $or: [{ arazCityID: toObjectId(arazCityID) }, { arazCityID: null }],
  });
  const waazVenuesCount = await WaazVenue.countDocuments({
    miqaatID,
    arazCityID,
  });
  const mawaidVenuesCount = await MawaidVenue.countDocuments({
    miqaatID,
    arazCityID,
  });
  const hotelsCount = await Hotel.countDocuments({ miqaatID, arazCityID });

  const arazCityStats = [
    {
      title: "Total Zone",
      count: zonesCount,
      type: "zone",
    },
    {
      title: "Total Waaz Venue",
      count: waazVenuesCount,
      type: "waaz-venue",
    },
    {
      title: "Total Mawaid Venue",
      count: mawaidVenuesCount,
      type: "mawaid-venue",
    },
    {
      title: "Total Hotel",
      count: hotelsCount,
      type: "hotel",
    },
  ];

  return apiResponse(
    FETCH,
    "Users Report with Zones, Waaz Venues, Mawaid Venues and Hotels",
    {
      arazCityStats,
    },
    res
  );
});

const getUsersAnalyticsReport = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.query;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const users = await getActiveUsers(arazCityID, miqaatID);
  const gender = getGenderStats(users);
  const totalKGUsers = [
    {
      name: "Total Mardo",
      value: gender.male,
      fill: "#2AAE5D",
    },
    {
      name: "Total Bairao",
      value: gender.female,
      fill: "#DC6780",
    },
  ];

  const deviceType = await getDeviceStats(arazCityID);
  const deviceInfoChart = [
    { name: "iOS Users", value: deviceType.iosUser, color: "#6C1F1F" },
    { name: "Android Users", value: deviceType.androidUser, color: "#BE5A5A" },
    { name: "Not Installed", value: deviceType.notInstalled, color: "#C28787" },
  ];

  const dailyUserStatsChart = await getDailyUserStats(arazCityID, miqaatID, 7);
  const kgPoolUserChart = await getKGPoolUserStats(arazCityID, miqaatID, 7);
  const kgTypeChart = await getKGTypeChart(arazCityID, miqaatID);

  return apiResponse(
    FETCH,
    "Users Report with KG Users and Device Info Chart",
    {
      totalKGUsers,
      deviceInfoChart,
      dailyUserStatsChart,
      kgPoolUserChart,
      kgTypeChart,
    },
    res
  );
});

const getKGPoolInterestDept = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.query;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const kgPoolInterestChart = await getkgPoolInterestChart(
    arazCityID,
    miqaatID
  );
  const positionWiseChart = await getPositionChart(arazCityID, miqaatID);

  return apiResponse(
    FETCH,
    "KG Pool Interest Department Wise and Position Wise Chart",
    {
      kgPoolInterestChart,
      positionWiseChart,
    },
    res
  );
});

const getPermissionWiseUser = apiHandler(async (req, res) => {
  const { arazCityID, miqaatID } = req.query;

  const checkActiveMiqaatAndArazcityStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveMiqaatAndArazcityStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const permissionWiseChart = await permissionWiseUser(arazCityID, miqaatID);

  return apiResponse(
    FETCH,
    "Permission Wise User Chart",
    {
      permissionWiseChart,
    },
    res
  );
});

// Helper Functions
const getActiveUsers = async (arazCityID, miqaatID) => {
  return await KGUser.find({
    miqaats: {
      $elemMatch: {
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
        hierarchyPositionID: { $ne: null },
        isActive: true,
      },
    },
  }).lean();
};

const getGenderStats = (users) => {
  return {
    male: users.filter((user) => user.gender === "M").length,
    female: users.filter((user) => user.gender === "F").length,
  };
};

const getDeviceStats = async (arazCityID) => {
  const kgUsers = await KGUser.find({
    miqaats: {
      $elemMatch: {
        arazCityID: toObjectId(arazCityID),
      },
    },
  }).lean();

  return {
    androidUser: kgUsers.filter(
      (user) => user?.appDetails?.deviceType === "ANDROID"
    ).length,
    iosUser: kgUsers.filter((user) => user?.appDetails?.deviceType === "IOS")
      .length,
    notInstalled: kgUsers.filter(
      (user) =>
        !user?.appDetails?.deviceType ||
        (user?.appDetails?.deviceType !== "ANDROID" &&
          user?.appDetails?.deviceType !== "IOS")
    ).length,
  };
};

const getDailyUserStats = async (arazCityID, miqaatID, days = 7) => {
  const today = dayjs().endOf("day").toDate();
  const startDate = dayjs()
    .subtract(days - 1, "day")
    .startOf("day")
    .toDate();

  const rawResult = await KGUser.aggregate([
    {
      $match: {
        updatedAt: { $gte: startDate, $lte: today },
        miqaats: {
          $elemMatch: {
            arazCityID: toObjectId(arazCityID),
            miqaatID: toObjectId(miqaatID),
            isActive: true,
            hierarchyPositionID: { $ne: null },
          },
        },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: "%Y-%m-%d",
            date: {
              $add: ["$updatedAt", 19800000], // Adjust to IST (UTC+5:30)
            },
          },
        },
        count: { $sum: 1 },
      },
    },
    {
      $sort: { _id: 1 },
    },
  ]);

  const resultMap = Object.fromEntries(rawResult.map((r) => [r._id, r.count]));

  const fullDays = Array.from({ length: days }).map((_, i) => {
    const date = dayjs().subtract(days - 1 - i, "day");
    const dateStr = date.format("YYYY-MM-DD");
    return {
      date: dateStr,
      day: date.format("dddd"),
      users: resultMap[dateStr] || 0,
    };
  });

  return fullDays;
};

const getKGPoolUserStats = async (arazCityID, miqaatID, days = 7) => {
  const today = dayjs().endOf("day").toDate(); // Today at 23:59:59
  const startDate = dayjs()
    .subtract(days - 1, "day")
    .startOf("day")
    .toDate(); // Start N days ago at 00:00:00

  const pipeline = [
    {
      $match: {
        updatedAt: {
          $gte: startDate,
          $lte: today,
        },
        miqaats: {
          $elemMatch: {
            arazCityID: toObjectId(arazCityID),
            miqaatID: toObjectId(miqaatID),
            isActive: true,
            $or: [
              { hierarchyPositionID: null },
              { hierarchyPositionID: { $exists: false } },
            ],
          },
        },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: "%Y-%m-%d",
            date: {
              $add: ["$updatedAt", 19800000], // Adjust to IST (UTC+5:30)
            },
          },
        },
        count: { $sum: 1 },
      },
    },
    {
      $sort: { _id: 1 },
    },
  ];

  const rawResult = await KGUser.aggregate(pipeline);

  // Convert results into { date: count }
  const resultMap = Object.fromEntries(rawResult.map((r) => [r._id, r.count]));

  // Create full day list from start to today
  const fullDays = Array.from({ length: days }).map((_, i) => {
    const date = dayjs().subtract(days - 1 - i, "day");
    const dateStr = date.format("YYYY-MM-DD");
    return {
      date: dateStr,
      day: date.format("dddd"),
      users: resultMap[dateStr] || 0,
    };
  });

  return fullDays;
};

const getKGTypeChart = async (arazCityID, miqaatID) => {
  const pipeline = [
    {
      $match: {
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
            hierarchyPositionID: { $ne: null },
            status: { $ne: "DECLINED" },
          },
        },
      },
    },
    {
      $addFields: {
        filteredMiqaat: {
          $first: {
            $filter: {
              input: "$miqaats",
              as: "m",
              cond: {
                $and: [
                  { $eq: ["$$m.miqaatID", toObjectId(miqaatID)] },
                  { $eq: ["$$m.arazCityID", toObjectId(arazCityID)] },
                  { $eq: ["$$m.isActive", true] },
                  { $ne: ["$$m.hierarchyPositionID", null] },
                  { $not: { $in: ["$$m.status", ["DECLINED", "DELETED"]] } },
                ],
              },
            },
          },
        },
      },
    },
    {
      $match: {
        "filteredMiqaat.kgTypeID": { $ne: null },
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        localField: "filteredMiqaat.kgTypeID",
        foreignField: "_id",
        as: "kgType",
      },
    },
    {
      $unwind: "$kgType",
    },
    {
      $match: {
        "kgType.isActive": true,
        "kgType.name": { $ne: null },
      },
    },
    {
      $group: {
        _id: "$kgType._id",
        name: { $first: "$kgType.name" },
        priority: { $first: "$kgType.priority" },
        color: { $first: "$kgType.color" },
        value: { $sum: 1 },
      },
    },
    {
      $project: {
        _id: 0,
        name: 1,
        value: 1,
        color: 1,
        priority: 1,
      },
    },
    {
      $sort: {
        priority: 1,
      },
    },
  ];
  const kgTypes = await KGUser.aggregate(pipeline);

  return kgTypes;
};

const getkgPoolInterestChart = async (arazCityID, miqaatID) => {
  const arazCity = await ArazCity.findById(arazCityID).lean();
  const departmentIDs = arazCity.departments.map((d) => d.departmentID);
  const departments = await Department.find({}).lean();

  const kgUsers = await KGUser.find({
    miqaats: {
      $elemMatch: {
        arazCityID,
        miqaatID,
      },
    },
  }).lean();

  const interestMap = {};
  const interests = await Interest.find({
    arazCityID,
    miqaatID,
    status: "not-assigned",
  }).lean();

  for (const i of interests) {
    interestMap[i.userID.toString()] = [
      i.interestOne?.departmentID?.toString(),
    ].filter(Boolean);
  }

  const result = departments.map((dep) => {
    const depId = dep._id.toString();
    const arazDep = arazCity.departments.find(
      (d) => d.departmentID.toString() === depId
    );

    const hrPoolUsers = kgUsers.filter(
      (u) =>
        u.miqaats.some(
          (m) =>
            m.arazCityID.toString() === arazCityID &&
            m.miqaatID.toString() === miqaatID &&
            !m.hierarchyPositionID
        ) && interestMap[u._id.toString()]?.includes(depId)
    );
    const availableInCityHRPool = hrPoolUsers.length;

    return {
      name: dep.name,
      value: availableInCityHRPool,
    };
  });
  return result;
};

const getPositionChart = async (arazCityID, miqaatID) => {
  const result = await KGUser.aggregate([
    {
      $unwind: "$miqaats",
    },
    {
      $match: {
        "miqaats.isActive": true,
        "miqaats.miqaatID": toObjectId(miqaatID),
        "miqaats.arazCityID": toObjectId(arazCityID),
        "miqaats.hierarchyPositionID": { $ne: null },
      },
    },
    {
      $group: {
        _id: {
          positionID: "$miqaats.hierarchyPositionID",
          gender: "$gender",
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: "$_id.positionID",
        male: {
          $sum: {
            $cond: [{ $eq: ["$_id.gender", "M"] }, "$count", 0],
          },
        },
        female: {
          $sum: {
            $cond: [{ $eq: ["$_id.gender", "F"] }, "$count", 0],
          },
        },
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "_id",
        foreignField: "_id",
        as: "position",
      },
    },
    {
      $unwind: {
        path: "$position",
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 0,
        // positionID: "$_id",
        name: "$position.name",
        alias: "$position.alias",
        mardo: "$male",
        bairao: "$female",
      },
    },
  ]);
  return result;
};

const permissionWiseUser = async (arazCityID, miqaatID) => {
  const result = await KGUser.aggregate([
    {
      $match: {
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
            cityRoleID: { $ne: null },
          },
        },
      },
    },
    {
      $project: {
        _id: 1,
        gender: 1,
        miqaats: {
          $filter: {
            input: "$miqaats",
            as: "m",
            cond: {
              $and: [
                { $eq: ["$$m.isActive", true] },
                { $eq: ["$$m.miqaatID", toObjectId(miqaatID)] },
                { $eq: ["$$m.arazCityID", toObjectId(arazCityID)] },
                { $ne: ["$$m.cityRoleID", null] },
              ],
            },
          },
        },
      },
    },
    { $unwind: "$miqaats" },
    {
      $group: {
        _id: {
          cityRoleID: "$miqaats.cityRoleID",
          gender: "$gender",
        },
        count: { $sum: 1 },
      },
    },
    {
      $group: {
        _id: "$_id.cityRoleID",
        male: {
          $sum: {
            $cond: [{ $eq: ["$_id.gender", "M"] }, "$count", 0],
          },
        },
        female: {
          $sum: {
            $cond: [{ $eq: ["$_id.gender", "F"] }, "$count", 0],
          },
        },
      },
    },
    {
      $lookup: {
        from: "systemroles",
        localField: "_id",
        foreignField: "_id",
        as: "cityRole",
      },
    },
    { $unwind: { path: "$cityRole", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 0,
        name: "$cityRole.name",
        mardo: "$male",
        bairao: "$female",
      },
    },
  ]);
  const finalResult = result.filter(
    (obj) => obj.name && obj.name !== "Not Assigned"
  );
  return finalResult;
};

const getKGTypes = async () => {
  const kgTypes = await KGType.find().lean();

  if (!kgTypes.length) {
    return null;
  }

  return kgTypes;
};

const getCountsByArazCityId = async (arazCityId) => {
  const city = await ArazCity.findById(arazCityId, {
    departments: 1,
    arazCityZones: 1,
  });

  if (!city) {
    return { error: "Araz City not found" };
  }

  const departmentCount = city.departments.length;
  const zoneCount = city.arazCityZones.length;

  return {
    departmentCount,
    zoneCount,
  };
};

const getLastSevenDaysChart = (users) => {
  const days = 7;
  const today = new Date();

  const end = new Date(today); // Clone today's date
  end.setHours(23, 59, 59, 999);

  const start = new Date(today);
  start.setDate(start.getDate() - (days - 1));
  start.setHours(0, 0, 0, 0);

  const result = [];

  for (let i = 0; i < days; i++) {
    const currentDate = new Date(start);
    currentDate.setDate(start.getDate() + i);

    const dayName = currentDate.toLocaleDateString("en-US", {
      weekday: "long",
    });
    const dateOnly = currentDate.toISOString().split("T")[0]; // "YYYY-MM-DD"

    const count = users.filter((user) => {
      if (!user.createdAt) return false;
      const createdDate = new Date(user.createdAt);
      if (isNaN(createdDate)) return false;

      const createdDateStr = createdDate.toISOString().split("T")[0];
      return createdDateStr === dateOnly;
    }).length;

    result.push({
      day: dayName,
      date: dateOnly,
      count,
    });
  }
  return result;
};

const getHierarchyPositionsFilter = (hierarchyPositionDetails) => {
  const result = {};

  hierarchyPositionDetails.forEach((user) => {
    const gender = user.gender === "F" ? "female" : "male";

    user.miqaats.forEach((miqaat) => {
      if (!miqaat.isActive || !miqaat.hierarchyPositionID) return;

      const alias = miqaat.hierarchyPositionID.alias;
      if (!alias) return;

      if (!result[alias]) {
        result[alias] = { male: 0, female: 0 };
      }

      result[alias][gender]++;
    });
  });

  return result;
};

const permissionsWiseUserFilter = (permissionsWiseUserDetails, mId, aId) => {
  const result = {};

  const isGlobal = !mId && !aId;

  permissionsWiseUserDetails.forEach((user) => {
    const gender = user.gender === "F" ? "female" : "male";

    if (isGlobal) {
      const roleName = user.systemRoleID?.name;
      if (!roleName) return;

      if (!result[roleName]) {
        result[roleName] = { male: 0, female: 0 };
      }

      result[roleName][gender]++;
    } else {
      user.miqaats?.forEach((miqaat) => {
        if (!miqaat?.isActive || !miqaat?.cityRoleID?.name) return;

        const roleName = miqaat.cityRoleID.name;

        if (!result[roleName]) {
          result[roleName] = { male: 0, female: 0 };
        }

        result[roleName][gender]++;
      });
    }
  });

  return result;
};

const getArazCityDetails = async (arazCityID) => {
  const arazCity = await ArazCity.findById(toObjectId(arazCityID))
    .lean()
    .populate({
      path: "miqaatID",
      select: "name",
    })
    .populate({
      path: "hierarchyPositions.hierarchyPositionID",
      select: "name priority",
    });

  if (!arazCity) {
    return null;
  }

  return {
    _id: arazCity._id,
    name: arazCity.name,
    miqaatName: arazCity.miqaatID?.name || "Unknown Miqaat",
    hierarchyPositions: arazCity.hierarchyPositions,
  };
};

const getHierarchyPositions = async (hierarchyPositionIds) => {
  const allHierarchyPositions = await HierarchyPosition.find({
    _id: { $in: hierarchyPositionIds },
  }).lean();

  const hierarchyPositionMap = {};
  allHierarchyPositions.forEach((position) => {
    hierarchyPositionMap[position._id.toString()] = position;
  });

  return hierarchyPositionMap;
};

const initializePositionArray = (
  hierarchyPositionIds,
  hierarchyPositionMap
) => {
  const usersByPositionArray = [];

  hierarchyPositionIds.forEach((positionId) => {
    const position = hierarchyPositionMap[positionId];
    if (position) {
      usersByPositionArray.push({
        _id: positionId,
        name: position.name,
        priority: position.priority || 0,
        users: [],
        count: 0,
      });
    }
  });

  return usersByPositionArray;
};

const initializeKGTypeArray = (kgTypes) => {
  const usersByKGTypeArray = kgTypes.map((type) => ({
    _id: type._id.toString(),
    name: type.name,
    priority: type.priority || 0,
    users: [],
    count: 0,
  }));

  usersByKGTypeArray.push({
    _id: "noKGType",
    name: "No KG Type",
    priority: Number.MAX_SAFE_INTEGER,
    users: [],
    count: 0,
  });

  return usersByKGTypeArray;
};

const getUserData = (user) => {
  return {
    _id: user._id,
    name: user.name,
    ITSID: user.ITSID,
    logo: user.logo,
    LDName: user.LDName,
    phone: user.gender == "M" ? user.phone : "",
    gender: user.gender,
    deviceType: user?.appDetails?.deviceType || "UNKNOWN",
  };
};

const processUsersByPositionAndType = (
  users,
  usersByPositionArray,
  usersByKGTypeArray,
  arazCityID,
  miqaatID
) => {
  users.forEach((user) => {
    const miqaat = user.miqaats.find(
      (miqaat) =>
        miqaat.miqaatID.toString() === miqaatID &&
        miqaat.arazCityID.toString() === arazCityID
    );

    if (!miqaat) return;

    const positionId = miqaat?.hierarchyPositionID?.toString();
    const userData = getUserData(user);

    processUserPosition(userData, positionId, usersByPositionArray);
    processUserKGType(userData, miqaat, usersByKGTypeArray);
  });

  usersByPositionArray.sort((a, b) => a.priority - b.priority);
  usersByKGTypeArray.sort((a, b) => a.priority - b.priority);

  return { usersByPositionArray, usersByKGTypeArray };
};

const processUserPosition = (userData, positionId, usersByPositionArray) => {
  if (positionId) {
    const positionIndex = usersByPositionArray.findIndex(
      (item) => item._id === positionId
    );
    if (positionIndex !== -1) {
      usersByPositionArray[positionIndex].users.push(userData);
      usersByPositionArray[positionIndex].count++;
    }
  }
};

const processUserKGType = (userData, miqaat, usersByKGTypeArray) => {
  if (miqaat.kgTypeID) {
    const kgTypeId = miqaat.kgTypeID.toString();
    const kgTypeIndex = usersByKGTypeArray.findIndex(
      (item) => item._id === kgTypeId
    );
    if (kgTypeIndex !== -1) {
      usersByKGTypeArray[kgTypeIndex].users.push(userData);
      usersByKGTypeArray[kgTypeIndex].count++;
    }
  } else {
    const noKGTypeIndex = usersByKGTypeArray.findIndex(
      (item) => item._id === "noKGType"
    );
    usersByKGTypeArray[noKGTypeIndex].users.push(userData);
    usersByKGTypeArray[noKGTypeIndex].count++;
  }
};

const getKGTypeStats = (usersByKGTypeArray) => {
  const kgTypeStats = {};

  usersByKGTypeArray.forEach((kgType) => {
    if (kgType._id === "noKGType") {
      kgTypeStats["No KG Type"] = kgType.count;
    } else {
      kgTypeStats[kgType.name] = kgType.count;
    }
  });

  return kgTypeStats;
};

const getMonthlyStats = async (users) => {
  const currentDate = new Date();
  const sevenMonthsAgo = new Date();
  sevenMonthsAgo.setMonth(currentDate.getMonth() - 6);
  sevenMonthsAgo.setDate(1);
  sevenMonthsAgo.setHours(0, 0, 0, 0);

  if (!users.length) {
    return [];
  }

  return calculateMonthlyUsersCount(users, currentDate);
};

const calculateMonthlyUsersCount = (users, currentDate) => {
  const months = initializeMonthsArray(currentDate);

  users.forEach((user) => {
    const createdDate = new Date(user.createdAt);
    const createdMonth = createdDate.getMonth();
    const createdYear = createdDate.getFullYear();

    const monthEntry = months.find(
      (m) => m.monthIndex === createdMonth && m.year === createdYear
    );

    if (monthEntry) {
      monthEntry.count++;
    }
  });

  return months.map((month) => ({
    month: `${month.name} ${month.year}`,
    count: month.count,
  }));
};

const initializeMonthsArray = (currentDate) => {
  const months = [];
  const monthNames = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  for (let i = 6; i >= 0; i--) {
    const monthIndex = (currentDate.getMonth() - i + 12) % 12;
    const year =
      currentDate.getFullYear() - (currentDate.getMonth() < i ? 1 : 0);

    months.push({
      name: monthNames[monthIndex],
      year: year,
      monthIndex: monthIndex,
      count: 0,
    });
  }

  return months;
};

const getSystemRoles = async () => {
  const systemRoles = await SystemRole.find().lean();
  if (!systemRoles.length) {
    return null;
  }
  return systemRoles;
};

const initializeSystemRoleArray = (systemRoles) => {
  const usersBySystemRoleArray = systemRoles.map((role) => ({
    _id: role._id.toString(),
    name: role.name,
    priority: role.priority || 0,
    users: [],
    count: 0,
  }));

  usersBySystemRoleArray.push({
    _id: "noSystemRole",
    name: "No System Role",
    priority: Number.MAX_SAFE_INTEGER,
    users: [],
    count: 0,
  });

  return usersBySystemRoleArray;
};

const processUsersBySystemRole = (
  users,
  usersBySystemRoleArray,
  miqaatID,
  arazCityID
) => {
  users.forEach((user) => {
    const userData = getUserData(user);

    // Check if user has systemRoleID
    if (user.systemRoleID) {
      const systemRoleId = user.systemRoleID.toString();
      const roleIndex = usersBySystemRoleArray.findIndex(
        (item) => item._id === systemRoleId
      );
      if (roleIndex !== -1) {
        usersBySystemRoleArray[roleIndex].users.push(userData);
        usersBySystemRoleArray[roleIndex].count++;
      }
    }
    const miqaat = user.miqaats.find(
      (miqaat) =>
        miqaat.miqaatID.toString() === miqaatID &&
        miqaat.arazCityID.toString() === arazCityID
    );
    if (miqaat) {
      const systemRoleId = miqaat.cityRoleID.toString();
      const roleIndex = usersBySystemRoleArray.findIndex(
        (item) => item._id === systemRoleId
      );
      if (roleIndex !== -1) {
        usersBySystemRoleArray[roleIndex].users.push(userData);
        usersBySystemRoleArray[roleIndex].count++;
      }
    } else {
      // If no system role, add to "No System Role" category
      const noRoleIndex = usersBySystemRoleArray.findIndex(
        (item) => item._id === "noSystemRole"
      );
      usersBySystemRoleArray[noRoleIndex].users.push(userData);
      usersBySystemRoleArray[noRoleIndex].count++;
    }
  });

  // Sort by priority
  usersBySystemRoleArray.sort((a, b) => a.priority - b.priority);

  return usersBySystemRoleArray;
};

const getSystemRoleStats = (usersBySystemRoleArray) => {
  const systemRoleStats = {};

  usersBySystemRoleArray.forEach((role) => {
    if (role._id === "noSystemRole") {
      systemRoleStats["No System Role"] = role.count;
    } else {
      systemRoleStats[role.name] = role.count;
    }
  });

  return systemRoleStats;
};

// REPORT 2: Overall Statistics for all KGUsers
const getOverallUsersReport = apiHandler(async (req, res) => {
  try {
    // Fetch all users from the database
    const allUsers = await KGUser.find().lean();

    if (!allUsers.length) {
      return apiError(
        CUSTOM_ERROR,
        "No users found in the database",
        null,
        res
      );
    }

    // Get all KG Types and System Roles for categorization
    const kgTypes = await getKGTypes();
    if (!kgTypes) {
      return apiError(CUSTOM_ERROR, "No KG types found", null, res);
    }

    const systemRoles = await getSystemRoles();
    if (!systemRoles) {
      return apiError(CUSTOM_ERROR, "No system roles found", null, res);
    }

    // Get all ArazCities for distribution statistics
    const arazCities = await ArazCity.find().lean().populate({
      path: "miqaatID",
      select: "name",
    });

    // Basic statistics
    const overallStatistics = {
      totalUsers: allUsers.length,
      activeUsers: allUsers.filter((user) => user.isActive).length,
      gender: getGenderStats(allUsers),
      deviceType: getDeviceStats(allUsers),
    };

    // Get user distribution by system roles
    const usersBySystemRoleArray = initializeSystemRoleArray(systemRoles);
    allUsers.forEach((user) => {
      const userData = getUserData(user);

      if (user.systemRoleID) {
        const systemRoleId = user.systemRoleID.toString();
        const roleIndex = usersBySystemRoleArray.findIndex(
          (item) => item._id === systemRoleId
        );
        if (roleIndex !== -1) {
          usersBySystemRoleArray[roleIndex].users.push(userData);
          usersBySystemRoleArray[roleIndex].count++;
        }
      } else {
        const miqaats = user.miqaats.filter(
          (miqaat) =>
            miqaat.cityRoleID && miqaat.cityRoleID.toString() !== "noSystemRole"
        );
        if (miqaats) {
          for (const miqaat of miqaats) {
            const systemRoleId = miqaat?.cityRoleID?.toString();
            const roleIndex = usersBySystemRoleArray.findIndex(
              (item) => item._id === systemRoleId
            );
            if (roleIndex !== -1) {
              usersBySystemRoleArray[roleIndex].users.push(userData);
              usersBySystemRoleArray[roleIndex].count++;
            }
          }
        } else {
          // If no system role, add to "No System Role" category
          const noRoleIndex = usersBySystemRoleArray.findIndex(
            (item) => item._id === "noSystemRole"
          );
          usersBySystemRoleArray[noRoleIndex].users.push(userData);
          usersBySystemRoleArray[noRoleIndex].count++;
        }
      }
    });

    // Sort by priority
    usersBySystemRoleArray.sort((a, b) => a.priority - b.priority);
    overallStatistics.systemRoles = getSystemRoleStats(usersBySystemRoleArray);

    // Get monthly registration statistics
    const monthlyStats = await getMonthlyStats(allUsers);

    // Get city distribution statistics
    const cityDistribution = {};
    arazCities.forEach((city) => {
      cityDistribution[city.name] = {
        totalUsers: 0,
        miqaatName: city.miqaatID?.name || "Unknown Miqaat",
        cityID: city._id.toString(),
      };
    });

    // Count users per ArazCity
    allUsers.forEach((user) => {
      if (user.miqaats && user.miqaats.length > 0) {
        user.miqaats.forEach((miqaat) => {
          if (miqaat.arazCityID) {
            const arazCityId = miqaat.arazCityID.toString();
            const arazCity = arazCities.find(
              (city) => city._id.toString() === arazCityId
            );
            if (arazCity && cityDistribution[arazCity.name]) {
              cityDistribution[arazCity.name].totalUsers++;
            }
          }
        });
      }
    });

    // Get KG type distribution
    const usersByKGTypeArray = initializeKGTypeArray(kgTypes);

    // Process each user's KG types across all their miqaats
    allUsers.forEach((user) => {
      const userData = getUserData(user);

      // Check all miqaats for this user
      let kgTypeFound = false;

      if (user.miqaats && user.miqaats.length > 0) {
        user.miqaats.forEach((miqaat) => {
          if (miqaat.kgTypeID) {
            kgTypeFound = true;
            const kgTypeId = miqaat.kgTypeID.toString();
            const kgTypeIndex = usersByKGTypeArray.findIndex(
              (item) => item._id === kgTypeId
            );

            if (kgTypeIndex !== -1) {
              // Only add the user if they haven't been added to this KG type already
              const alreadyAdded = usersByKGTypeArray[kgTypeIndex].users.some(
                (u) => u._id.toString() === userData._id.toString()
              );

              if (!alreadyAdded) {
                usersByKGTypeArray[kgTypeIndex].users.push(userData);
                usersByKGTypeArray[kgTypeIndex].count++;
              }
            }
          }
        });
      }

      // If no KG type found in any miqaat, add to "No KG Type"
      if (!kgTypeFound) {
        const noKGTypeIndex = usersByKGTypeArray.findIndex(
          (item) => item._id === "noKGType"
        );
        usersByKGTypeArray[noKGTypeIndex].users.push(userData);
        usersByKGTypeArray[noKGTypeIndex].count++;
      }
    });

    // Sort by priority
    usersByKGTypeArray.sort((a, b) => a.priority - b.priority);
    overallStatistics.kgTypes = getKGTypeStats(usersByKGTypeArray);

    // Get user activity statistics
    const activityStats = {
      activeLastWeek: 0,
      activeLastMonth: 0,
      activeLastThreeMonths: 0,
      inactiveOverThreeMonths: 0,
    };

    const now = new Date();
    const oneWeekAgo = new Date(now);
    oneWeekAgo.setDate(now.getDate() - 7);

    const oneMonthAgo = new Date(now);
    oneMonthAgo.setMonth(now.getMonth() - 1);

    const threeMonthsAgo = new Date(now);
    threeMonthsAgo.setMonth(now.getMonth() - 3);

    allUsers.forEach((user) => {
      if (user.lastLoginAt) {
        const lastLogin = new Date(user.lastLoginAt);

        if (lastLogin >= oneWeekAgo) {
          activityStats.activeLastWeek++;
        } else if (lastLogin >= oneMonthAgo) {
          activityStats.activeLastMonth++;
        } else if (lastLogin >= threeMonthsAgo) {
          activityStats.activeLastThreeMonths++;
        } else {
          activityStats.inactiveOverThreeMonths++;
        }
      } else {
        // Users who never logged in
        activityStats.inactiveOverThreeMonths++;
      }
    });

    overallStatistics.userActivity = activityStats;

    return apiResponse(
      FETCH,
      "Overall Users Report",
      {
        statistics: overallStatistics,
        cityDistribution,
        monthlyStats,
        usersBySystemRole: usersBySystemRoleArray,
        usersByKGType: usersByKGTypeArray,
      },
      res
    );
  } catch (error) {
    console.error("Error generating overall users report:", error);
    return apiError(
      CUSTOM_ERROR,
      "Error generating overall users report",
      error.message,
      res
    );
  }
});

module.exports = {
  getUsersSummaryReport,
  getUsersAnalyticsReport,
  getKGPoolInterestDept,
  getPermissionWiseUser,
  getOverallUsersReport,
};
