import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyFunctionApiUrl } from 'utils/constant';

const getFunctionURL = (endponints) => {
  return `${HierarchyFunctionApiUrl}${endponints}`;
};

const getFunctions = (data, dispatch) => {
  return getRequest('global', getFunctionURL('get'), data, true, dispatch);
};

const getAllFunctionsByDept = (data, dispatch) => {
  return getRequest('global', getFunctionURL('get/department-functions/all'), data, true, dispatch);
};

const getSingleFunction = (data, dispatch) => {
  return getRequest('global', getFunctionURL(`get/${data}`), data, true, dispatch);
};

const getFunctionsByDepartment = (data, dispatch) => {
  return getRequest('global', getFunctionURL(`get/by-department/${data}`), data, true, dispatch);
};

const addFunction = (data, dispatch) => {
  return postRequest('global', getFunctionURL('add'), data, true, dispatch);
};

const updateFunction = (data, dispatch) => {
  return putRequest('global', getFunctionURL('edit'), data, true, dispatch);
};

const deleteFunction = (data, dispatch) => {
  return deleletRequest('global', getFunctionURL(`delete/${data}`), data, true, dispatch);
};

export const getFunctionsAction = createGenericAsyncThunk('functions/getFunctionsAction', getFunctions, 'get');

export const getAllFunctionsByDeptAction = createGenericAsyncThunk('functions/getAllFunctionsByDeptAction', getAllFunctionsByDept, 'get');

export const getSingleFunctionAction = createGenericAsyncThunk('functions/getSingleFunctionAction', getSingleFunction, 'get');

export const getFunctionsByDepartmentAction = createGenericAsyncThunk(
  'functions/getFunctionsByDepartmentAction',
  getFunctionsByDepartment,
  'get'
);

export const addFunctionAction = createGenericAsyncThunk('functions/addFunctionAction', addFunction);

export const updateFunctionAction = createGenericAsyncThunk('functions/updateFunctionAction', updateFunction);

export const deleteFunctionAction = createGenericAsyncThunk('functions/deleteFunctionAction', deleteFunction);
