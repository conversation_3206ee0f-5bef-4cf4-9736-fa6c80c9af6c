import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityMawaidVenueSuitabilityApiUrl } from 'utils/constant';

const getMawaidVenueSuitabilityURL = (endponints) => {
  return `${ZoneCapacityMawaidVenueSuitabilityApiUrl}${endponints}`;
};

const getAllMawaidVenueSuitability = (data, dispatch) => {
  return getRequest('global', getMawaidVenueSuitabilityURL('get'), data, true, dispatch);
};

const getSingleMawaidVenueSuitability = (data, dispatch) => {
  return getRequest('global', getMawaidVenueSuitabilityURL(`get/${data}`), data, true, dispatch);
};

const addMawaidVenueSuitability = (data, dispatch) => {
  return postRequest('global', getMawaidVenueSuitabilityURL('add'), data, true, dispatch);
};

const updateMawaidVenueSuitability = (data, dispatch) => {
  return putRequest('global', getMawaidVenueSuitabilityURL('edit'), data, true, dispatch);
};

const deleteMawaidVenueSuitability = (data, dispatch) => {
  return deleletRequest('global', getMawaidVenueSuitabilityURL(`delete/${data}`), data, true, dispatch);
};

export const getAllMawaidVenueSuitabilityAction = createGenericAsyncThunk(
  'MawaidVenueSuitability/getAllMawaidVenueSuitabilityAction',
  getAllMawaidVenueSuitability,
  'get'
);

export const getSingleMawaidVenueSuitabilityAction = createGenericAsyncThunk(
  'MawaidVenueSuitability/getSingleMawaidVenueSuitabilityAction',
  getSingleMawaidVenueSuitability,
  'get'
);

export const addMawaidVenueSuitabilityAction = createGenericAsyncThunk(
  'MawaidVenueSuitability/addMawaidVenueSuitabilityAction',
  addMawaidVenueSuitability
);

export const updateMawaidVenueSuitabilityAction = createGenericAsyncThunk(
  'MawaidVenueSuitability/updateMawaidVenueSuitabilityAction',
  updateMawaidVenueSuitability
);

export const deleteMawaidVenueSuitabilityAction = createGenericAsyncThunk(
  'MawaidVenueSuitability/deleteMawaidVenueSuitabilityAction',
  deleteMawaidVenueSuitability
);
