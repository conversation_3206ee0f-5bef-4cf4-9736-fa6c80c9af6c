import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyMiqaatApiUrl, NotificationApiUrl } from 'utils/constant';

const getNotificationApiURL = (endponints) => {
  return `${NotificationApiUrl}${endponints}`;
};

const getNotifications = (data, dispatch) => {
  return getRequest('global', getNotificationApiURL(`get?page=${data?.page}&limit=${data?.limit}`), data, true, dispatch);
};

const getSingleNotification = (data, dispatch) => {
  return getRequest('global', getNotificationApiURL(`get/${data}`), data, true, dispatch);
};

export const getNotificationsAction = createGenericAsyncThunk('notification/getNotificationsAction', getNotifications, 'get');

export const getSingleNotificationAction = createGenericAsyncThunk(
  'notification/getSingleNotificationAction',
  getSingleNotification,
  'get'
);
