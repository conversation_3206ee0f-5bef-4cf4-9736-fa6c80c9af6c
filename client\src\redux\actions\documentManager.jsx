import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { DocumentManagerApiUrl, DocumentManagerDefaultViewApiUrl } from 'utils/constant';

const getDocumentManagerURL = (endponints) => {
  return `${DocumentManagerApiUrl}${endponints}`;
};

const getDocumentManagers = (data, dispatch) => {
  return postRequest('global', getDocumentManagerURL('get'), data, true, dispatch);
};

const getDocumentManagerType = (data, dispatch) => {
  return getRequest('global', getDocumentManagerURL('get/document-type'), data, true, dispatch);
};

const getDefaultDocumentManagers = (data, dispatch) => {
  return postRequest('global', `${DocumentManagerDefaultViewApiUrl}${'get'}`, data, true, dispatch);
};

const getAllDocuments = (data, dispatch) => {
  return postRequest('global', `${DocumentManagerDefaultViewApiUrl}${'get/all-documents'}`, data, true, dispatch);
};

const getSingleDocumentManagers = (data, dispatch) => {
  return getRequest('global', getDocumentManagerURL(`get/${data}`), data, true, dispatch);
};

const addDocumentManager = (data, dispatch) => {
  return postRequest('global', getDocumentManagerURL('add'), data, true, dispatch);
};

const updateDocumentManager = (data, dispatch) => {
  let { id, ...rest } = data || {};
  let payload = { ...rest };
  return putRequest('global', getDocumentManagerURL(`edit/${id}`), payload, true, dispatch);
};

const deleteDocumentManager = (data, dispatch) => {
  return deleletRequest('global', getDocumentManagerURL(`delete/${data}`), data, true, dispatch);
};

const uploadFile = (data, dispatch) => {
  return postRequest('global', getDocumentManagerURL(`add/upload-document`), data, true, dispatch, 'formdata');
};

const recordUserClick = (data, dispatch) => {
  return patchRequest('global', getDocumentManagerURL(`add/click`), data, true, dispatch);
};

const userClickReport = (data, dispatch) => {
  return postRequest('global', getDocumentManagerURL(`get/user-click-report/${data}`), data, true, dispatch);
};

const getAttachment = (data, dispatch) => {
  return postRequest('global', `${DocumentManagerDefaultViewApiUrl}get/document-url`, data, true, dispatch);
};

export const getDocumentManagersAction = createGenericAsyncThunk('DocumentManager/getDocumentManagersAction', getDocumentManagers, 'get');

export const getDefaultDocumentManagersAction = createGenericAsyncThunk(
  'DocumentManager/getDefaultDocumentManagersAction',
  getDefaultDocumentManagers,
  'get'
);

export const getAllDocumentsAction = createGenericAsyncThunk('DocumentManager/getAllDocumentsAction', getAllDocuments, 'get');

export const getSingleDocumentManagersAction = createGenericAsyncThunk(
  'DocumentManager/getSingleDocumentManagersAction',
  getSingleDocumentManagers,
  'get'
);

export const addDocumentManagerAction = createGenericAsyncThunk('DocumentManager/addDocumentManagerAction', addDocumentManager);

export const updateDocumentManagerAction = createGenericAsyncThunk('DocumentManager/updateDocumentManagerAction', updateDocumentManager);

export const deleteDocumentManagerAction = createGenericAsyncThunk('DocumentManager/deleteDocumentManagerAction', deleteDocumentManager);

export const uploadDocumentManagerFileAction = createGenericAsyncThunk('DocumentManager/uploadFileAction', uploadFile, 'get');

export const recordUserClickAction = createGenericAsyncThunk('DocumentManager/recordUserClickAction', recordUserClick, 'get');

export const userClickReportAction = createGenericAsyncThunk('DocumentManager/userClickReportAction', userClickReport, 'get');

export const getDocumentManagerAttachmentAction = createGenericAsyncThunk('DocumentManager/getAttachmentAction', getAttachment, 'get');

export const getDocumentManagerTypesAction = createGenericAsyncThunk(
  'DocumentManagerType/getAttachmentAction',
  getDocumentManagerType,
  'get'
);
