const Joi = require("joi");
const {
  idValidation,
  stringValidation,
  arrayIdValidation,
  arrayStringValidation,
} = require("../../../utils/validator.util");

const addEditDocumentSchema = Joi.object({
  documentTitle: stringValidation,
  miqaatID: idValidation,
  arazCityIDs: arrayIdValidation.optional(),
  departments: arrayIdValidation.optional(),
  positions: arrayIdValidation.optional(),
  systemRoleIDs: arrayStringValidation.optional(),
  documentTypeID: idValidation.optional(),
  attachments: Joi.array().items(Joi.object().unknown()).optional(),
});

const getSingleDocumentSchema = Joi.object({
  id: idValidation,
});


const deleteDocumentSchema = Joi.object({
  id: idValidation,
});

const getDownloadURLSchema = Joi.object({
  fileKey: stringValidation,
});

const getAllDocumentSchema = Joi.object({
  miqaatID: idValidation.optional().allow(""),
  arazCityID:idValidation.optional().allow(""),
});

module.exports = {
  addEditDocumentSchema,
  getSingleDocumentSchema,
  deleteDocumentSchema,
  getDownloadURLSchema,
  getAllDocumentSchema
};
