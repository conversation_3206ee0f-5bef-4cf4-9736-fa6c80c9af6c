import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyFunctionApiUrl, TaskManagementApiUrl } from 'utils/constant';

const getTaskManagementURL = (endponints) => {
  return `${TaskManagementApiUrl}${endponints}`;
};

const addTask = (data, dispatch) => {
  return getRequest('taskManagement', getTaskManagementURL(`task/get/${data}`), data, true, dispatch);
};

export const addTaskAction = createGenericAsyncThunk('taskmanagement/addTaskAction', addTask, 'get');
