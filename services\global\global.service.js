const express = require("express");
const cors = require("cors");
const path = require("path");
const compression = require("compression");
const { CORS_ORIGIN, NODE_ENV, GLOBAL_PORT } = require("../../constants");
const routes = require("./index");
const { authGuard, roleGuard } = require("../../middlewares/guard.middleware");
const { apiLoggerMiddleware, apiErrorLoggerMiddleware, attachStartTime } = require("../../middlewares/apiLogger.middleware");
const { connectDB } = require("../../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));

// Add API logging middleware
app.use(attachStartTime);
app.use(apiLoggerMiddleware);

app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});

app.use(compression());
// Global routes
app.use("/api/global", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Global Service is running on port: ${GLOBAL_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ 
    status: "healthy", 
    service: "accommodation",
    port: GLOBAL_PORT,
    timestamp: new Date().toISOString()
  });
});

// Add error logging middleware at the end
app.use(apiErrorLoggerMiddleware);

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(GLOBAL_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(`Accommodation Service running on port: ${GLOBAL_PORT}`);
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
