// ==============================|| THEME CONFIG  ||============================== //

import { HierarchyDashboardPageUrl } from 'utils/constant';

const config = {
  defaultPath: HierarchyDashboardPageUrl,
  fontFamily: `'Montserrat', sans-serif`,
  i18n: 'en',
  miniDrawer: false,
  container: true,
  mode: 'light',
  presetColor: 'default',
  themeDirection: 'ltr'
};

export default config;
export const drawerWidth = 260;

export const route_url = '/';
export const BASE_URL = import.meta.env.VITE_REACT_APP_BASE_URL;
export const IMAGE_BASE_URL = import.meta.env.VITE_REACT_APP_IMAGE_BASE_URL;
export const CRYPTO_SECRET = import.meta.env.VITE_REACT_APP_CRYPTO_SECRET;
export const ONE_SIGNAL_APP_ID = import.meta.env.VITE_REACT_APP_ONE_SIGNAL_APP_ID;
export const GOOGLE_MAP_API_KEY = import.meta.env.VITE_REACT_APP_GOOGLE_MAP_API_KEY;
export const NODE_ENV = import.meta.env.VITE_NODE_ENV;
