const mongoose = require("mongoose");

const apiLogSchema = new mongoose.Schema(
  {
    // Request Information
    method: {
      type: String,
      required: true,
      enum: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD']
    },
    endpoint: {
      type: String,
      required: true,
      index: true
    },
    fullUrl: {
      type: String,
      required: true
    },
    
    // User Information (extracted from JWT)
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'KGUser',
      index: true
    },
    userItsId: {
      type: String,
      index: true
    },
    userName: {
      type: String
    },
    isSystemUser: {
      type: Boolean,
      default: false
    },
    
    // Request Data
    requestHeaders: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    requestBody: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    queryParams: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    
    // Response Data
    responseStatus: {
      type: Number,
      index: true
    },
    responseHeaders: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    responseBody: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    
    // Timing Information
    requestTime: {
      type: Date,
      required: true,
      index: true
    },
    responseTime: {
      type: Date
    },
    duration: {
      type: Number, // in milliseconds
      index: true
    },
    
    // Additional Metadata
    userAgent: {
      type: String
    },
    ipAddress: {
      type: String,
      index: true
    },
    service: {
      type: String,
      index: true // e.g., 'auth', 'global', 'hierarchy', etc.
    },
    
    // Error Information (if any)
    error: {
      type: String
    },
    errorStack: {
      type: String
    },
    
    // Success/Failure flag
    success: {
      type: Boolean,
      default: true,
      index: true
    }
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
apiLogSchema.index({ requestTime: -1 });
apiLogSchema.index({ userId: 1, requestTime: -1 });
apiLogSchema.index({ endpoint: 1, requestTime: -1 });
apiLogSchema.index({ success: 1, requestTime: -1 });
apiLogSchema.index({ service: 1, requestTime: -1 });

// TTL index to automatically delete logs after 90 days (optional)
apiLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 7776000 }); // 90 days

const ApiLog = mongoose.model("ApiLog", apiLogSchema);

module.exports = { ApiLog };
