const { required, boolean } = require("joi");
const { Schema, model } = require("mongoose");

const arazCitySchema = new Schema(
  {
    timeZoneID: {
      type: Schema.Types.ObjectId,
      ref: "TimeZone",
    },
    showPositionAlias: {
      type: Boolean,
      default: false,
    },
    addToOpenProject: {
      type: Boolean,
      default: false,
    },
    openProjectArazCityID: {
      type: Number,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    LDName: {
      type: String,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
    },
    miqaatID: {
      type: Schema.Types.ObjectId,
      ref: "Miqaat",
      required: true,
    },
    openProjectMiqaatID: {
      type: Number,
    },
    fasalDate: {
      type: Date,
      required: false,
    },
    logo: {
      type: String,
      required: false,
      trim: true,
    },
    status: {
      type: Boolean,
      default: true,
    },
    hasFasalProcessed: {
      type: Boolean,
      default: false,
    },
    isFasal: {
      type: <PERSON>olean,
      default: false,
    },
    jamiats: [
      {
        type: String,
        ref: "Jamiat",
        required: false,
      },
    ],
    jamaats: [
      {
        type: String,
        ref: "Jamaat",
        required: false,
      },
    ],
    arazCityZones: [
      {
        type: Schema.Types.ObjectId,
        ref: "ArazCityZone",
        required: false,
      },
    ],
    hierarchyPositions: [
      {
        _id: false,
        hierarchyPositionID: {
          type: Schema.Types.ObjectId,
          ref: "HierarchyPosition",
          required: false,
        },
        weightage: {
          type: Number,
          required: false,
          default: 10,
        },
        countRecommendation: {
          type: Number,
          required: false,
          default: 1,
        },
      },
    ],
    departments: [
      {
        _id: false,
        showForKhidmatInterest: {
          type: Boolean,
          default: false,
        },
        departmentID: {
          type: Schema.Types.ObjectId,
          ref: "Department",
          required: false,
        },
        openProjectDepartmentID: {
          type: Number,
        },
        status: {
          type: String,
          enum: ["active", "inactive"],
          required: false,
        },
        hierarchyPositions: [
          {
            type: Schema.Types.ObjectId,
            ref: "HierarchyPosition",
            required: false,
          },
        ],
        isZonal: {
          type: Boolean,
          default: true,
        },
        arazCityZones: {
          type: [
            {
              arazCityZoneID: {
                type: Schema.Types.ObjectId,
                ref: "ArazCityZone",
              },
              openProjectZoneID: {
                type: Number,
              },
            },
          ],
          default: [],
        },
        requirement: {
          type: Number,
          required: false,
          default: 0,
        },
      },
    ],
    openProjectGroups: {
      type: [
        {
          id: {
            type: Number,
          },
          name: {
            type: String,
          },
          projectIds: {
            type: [Number],
          }
        },
      ],
      default: [],
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const ArazCity = model("ArazCity", arazCitySchema);

module.exports = { ArazCity };
