import { get } from 'lodash';
import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchySystemRoleApiUrl } from 'utils/constant';

const getSystemRoleURL = (endponints) => {
  return `${HierarchySystemRoleApiUrl}${endponints}`;
};

const getSystemRoles = (data, dispatch) => {
  return getRequest('hierarchy', getSystemRoleURL('get'), data, true, dispatch);
};

const getSingleSystemRole = (data, dispatch) => {
  return getRequest('hierarchy', getSystemRoleURL(`get/${data}`), data, true, dispatch);
};

const addSystemRole = (data, dispatch) => {
  return postRequest('hierarchy', getSystemRoleURL('add'), data, true, dispatch);
};

const updateSystemRole = (data, dispatch) => {
  return putRequest('hierarchy', getSystemRoleURL('edit'), data, true, dispatch);
};

const deleteSystemRole = (data, dispatch) => {
  return deleletRequest('hierarchy', getSystemRoleURL(`delete/${data}`), data, true, dispatch);
};

export const getSystemRolesAction = createGenericAsyncThunk('SystemRoles/getSystemRolesAction', getSystemRoles, 'get');

export const getSingleSystemRoleAction = createGenericAsyncThunk('SystemRoles/getSingleSystemRoleAction', getSingleSystemRole, 'get');

export const addSystemRoleAction = createGenericAsyncThunk('SystemRoles/addSystemRolesAction', addSystemRole);

export const updateSystemRoleAction = createGenericAsyncThunk('SystemRoles/updadeSystemRoleAction', updateSystemRole);

export const deleteSystemRoleAction = createGenericAsyncThunk('SystemRoles/deleteSystemRoleAction', deleteSystemRole);
