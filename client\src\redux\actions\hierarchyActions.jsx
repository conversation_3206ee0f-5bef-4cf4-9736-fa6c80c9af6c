import { createGenericAsyncThunk } from 'redux/helper';
import { patchRequest, postRequest } from 'utils/axios';

import { HierarchyApiUrl, HierarchyPositionApiUrl } from 'utils/constant';

const getHierarchyURL = (endponints) => {
  return `${HierarchyApiUrl}${endponints}`;
};

const getHierarchy = (data, dispatch) => {
  return postRequest('hierarchy', getHierarchyURL('get'), data, true, dispatch);
};

const generateHierarchy = (data, dispatch) => {
  return postRequest('hierarchy', getHierarchyURL('edit/regenerate'), data, true, dispatch);
};

const getAliasName = (data, dispatch) => {
  return postRequest('hierarchy', 'hierarchy/dashboard/get/alias-name', data, true, dispatch);
};

const saveCountRecommendation = (data, dispatch) => {
  return patchRequest('global', `${HierarchyPositionApiUrl}edit/save-weightage`, data, true, dispatch);
};

export const getHierarchyAction = createGenericAsyncThunk('hierarchy/getHierarchyAction', getHierarchy, 'get', false);

export const getAliasNameAction = createGenericAsyncThunk('hierarchy/getAliasNameAction', getAliasName, 'get', false);

export const generateHierarchyAction = createGenericAsyncThunk('hierarchy/generateHierarchyAction', generateHierarchy);

export const saveCountRecommendationAction = createGenericAsyncThunk('hierarchy/saveCountRecommendationAction', saveCountRecommendation);
