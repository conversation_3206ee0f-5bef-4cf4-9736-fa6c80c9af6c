/**
 * Service Discovery and Health Checking
 * 
 * This module handles service registration, health checking, and load balancing
 */

const axios = require('axios');
const { SERVICES, GATEWAY_CONFIG } = require('./config');

class ServiceDiscovery {
  constructor() {
    this.services = new Map();
    this.healthCheckInterval = null;
    this.loadBalancerCounters = new Map();
    
    // Initialize services
    this.initializeServices();
    
    // Start health checking
    this.startHealthChecking();
  }

  /**
   * Initialize services from configuration
   */
  initializeServices() {
    Object.entries(SERVICES).forEach(([key, config]) => {
      this.services.set(key, {
        ...config,
        instances: [{
          id: `${key}-1`,
          host: config.host,
          port: config.port,
          healthy: false,
          lastHealthCheck: null,
          responseTime: null
        }],
        currentInstanceIndex: 0
      });
      
      // Initialize load balancer counter
      this.loadBalancerCounters.set(key, 0);
    });
    
    console.log(`🔍 Initialized ${this.services.size} services for discovery`);
  }

  /**
   * Start periodic health checking
   */
  startHealthChecking() {
    console.log('🏥 Starting health check monitoring...');
    
    // Initial health check
    this.performHealthChecks();
    
    // Set up periodic health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthChecks();
    }, GATEWAY_CONFIG.healthCheckInterval);
  }

  /**
   * Perform health checks on all services
   */
  async performHealthChecks() {
    const healthCheckPromises = [];
    
    for (const [serviceName, service] of this.services) {
      for (const instance of service.instances) {
        healthCheckPromises.push(this.checkInstanceHealth(serviceName, instance));
      }
    }
    
    await Promise.allSettled(healthCheckPromises);
    
    // Log health status
    this.logHealthStatus();
  }

  /**
   * Check health of a specific service instance
   */
  async checkInstanceHealth(serviceName, instance) {
    const startTime = Date.now();
    
    try {
      const service = this.services.get(serviceName);
      const healthUrl = `http://${instance.host}:${instance.port}${service.healthPath}`;
      
      const response = await axios.get(healthUrl, {
        timeout: 5000,
        validateStatus: (status) => status < 500
      });
      
      const responseTime = Date.now() - startTime;
      
      instance.healthy = response.status === 200;
      instance.lastHealthCheck = new Date();
      instance.responseTime = responseTime;
      
      if (!instance.healthy) {
        console.warn(`⚠️  Service ${serviceName} instance ${instance.id} unhealthy: ${response.status}`);
      }
      
    } catch (error) {
      instance.healthy = false;
      instance.lastHealthCheck = new Date();
      instance.responseTime = Date.now() - startTime;
      
      console.error(`❌ Service ${serviceName} instance ${instance.id} health check failed:`, error.message);
    }
  }

  /**
   * Get a healthy instance of a service using load balancing
   */
  getServiceInstance(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service ${serviceName} not found`);
    }

    const healthyInstances = service.instances.filter(instance => instance.healthy);
    
    if (healthyInstances.length === 0) {
      // If no healthy instances, try the first instance anyway (circuit breaker pattern)
      console.warn(`⚠️  No healthy instances for ${serviceName}, using first instance`);
      return service.instances[0];
    }

    // Load balancing strategy
    switch (GATEWAY_CONFIG.loadBalancing) {
      case 'round-robin':
        return this.getRoundRobinInstance(serviceName, healthyInstances);
      case 'least-connections':
        return this.getLeastConnectionsInstance(healthyInstances);
      case 'random':
        return this.getRandomInstance(healthyInstances);
      default:
        return healthyInstances[0];
    }
  }

  /**
   * Round-robin load balancing
   */
  getRoundRobinInstance(serviceName, instances) {
    const counter = this.loadBalancerCounters.get(serviceName);
    const instance = instances[counter % instances.length];
    this.loadBalancerCounters.set(serviceName, counter + 1);
    return instance;
  }

  /**
   * Least connections load balancing (simplified - based on response time)
   */
  getLeastConnectionsInstance(instances) {
    return instances.reduce((best, current) => {
      if (!best.responseTime) return current;
      if (!current.responseTime) return best;
      return current.responseTime < best.responseTime ? current : best;
    });
  }

  /**
   * Random load balancing
   */
  getRandomInstance(instances) {
    return instances[Math.floor(Math.random() * instances.length)];
  }

  /**
   * Find service by route path
   */
  findServiceByRoute(path) {
    for (const [serviceName, service] of this.services) {
      for (const route of service.routes) {
        if (path.startsWith(route)) {
          return serviceName;
        }
      }
    }
    return null;
  }

  /**
   * Get service URL for a given service name
   */
  getServiceUrl(serviceName) {
    const instance = this.getServiceInstance(serviceName);
    return `http://${instance.host}:${instance.port}`;
  }

  /**
   * Get all services status
   */
  getServicesStatus() {
    const status = {};
    
    for (const [serviceName, service] of this.services) {
      status[serviceName] = {
        name: service.name,
        instances: service.instances.map(instance => ({
          id: instance.id,
          host: instance.host,
          port: instance.port,
          healthy: instance.healthy,
          lastHealthCheck: instance.lastHealthCheck,
          responseTime: instance.responseTime
        })),
        healthyCount: service.instances.filter(i => i.healthy).length,
        totalCount: service.instances.length
      };
    }
    
    return status;
  }

  /**
   * Log health status summary
   */
  logHealthStatus() {
    const totalServices = this.services.size;
    let healthyServices = 0;
    
    for (const [serviceName, service] of this.services) {
      const healthyInstances = service.instances.filter(i => i.healthy).length;
      if (healthyInstances > 0) {
        healthyServices++;
      }
    }
    
    console.log(`🏥 Health Check: ${healthyServices}/${totalServices} services healthy`);
  }

  /**
   * Stop health checking
   */
  stop() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('🛑 Stopped health check monitoring');
    }
  }
}

module.exports = ServiceDiscovery;
