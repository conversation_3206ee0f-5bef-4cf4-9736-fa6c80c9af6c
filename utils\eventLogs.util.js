const {EventLogs} = require("../modules/globalMasters/models/eventLogs.model");
const { Hierarchy } = require("../modules/hierarchy/models");

const EventActions = {
  CREATE: "CREATE",
  UPDATE: "UPDATE",
  DELETE: "DELETE",
}

const Modules = {
  Hierarchy: "Hierarchy",
  GlobalMasters: "GlobalMasters",
  Communication: "Communication",
  AsharaGuide: "AsharaGuide",
  Accomodation: "Accomodation"
}


const addEventLog = async (action,module, actionOn, userId ) => {
  try {
    const message = `${action} ${actionOn} from ${module}`
    const eventLog = new EventLogs({
      action,
      module,
      message,
      actionOn,
      performedBy: userId,
    });
    await eventLog.save();
  } catch (error) {
    console.log(error);
  }
};

module.exports = { addEventLog , EventActions, Modules };
