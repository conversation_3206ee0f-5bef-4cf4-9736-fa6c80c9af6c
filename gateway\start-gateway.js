#!/usr/bin/env node

/**
 * Gateway Startup Script
 * 
 * This script starts the API Gateway with proper error handling and logging
 */

const APIGateway = require('./gateway');
const { connectDB } = require('../db');

async function startGateway() {
  try {
    console.log('🎯 AMS API Gateway Starting...');
    console.log('================================');
    
    // Connect to database (if needed for gateway operations)
    console.log('🔌 Connecting to database...');
    await connectDB();
    console.log('✅ Database connected');
    
    // Create and start gateway
    const gateway = new APIGateway();
    await gateway.start();
    
    console.log('================================');
    console.log('🎉 AMS API Gateway is ready!');
    console.log('================================');
    
  } catch (error) {
    console.error('❌ Failed to start gateway:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

// Start the gateway
startGateway();
