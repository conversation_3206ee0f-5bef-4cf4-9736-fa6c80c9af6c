/**
 * API Logger Initialization Script
 * 
 * This script initializes the API logging system by:
 * 1. Starting the BullMQ worker for processing API logs
 * 2. Setting up graceful shutdown handlers
 * 3. Providing health check functionality
 */

const { apiLogWorker, apiLogQueue } = require('./queues/apiLogQueue');
const { ApiLog } = require('../modules/globalMasters/models/apiLog.model');

/**
 * Initialize the API logging system
 */
const initApiLogger = async () => {
  try {
    console.log('🚀 Initializing API Logger...');
    
    // Test database connection
    await testDatabaseConnection();
    
    // Test Redis connection
    await testRedisConnection();
    
    // Start the worker (it's already started when imported, but we can check its status)
    console.log('✅ API Log Worker is running');
    console.log(`📊 Worker concurrency: ${apiLogWorker.opts.concurrency || 1}`);
    
    // Set up health check
    setupHealthCheck();
    
    console.log('✅ API Logger initialized successfully');
    
    return true;
  } catch (error) {
    console.error('❌ Failed to initialize API Logger:', error);
    return false;
  }
};

/**
 * Test database connection by attempting to connect to ApiLog collection
 */
const testDatabaseConnection = async () => {
  try {
    await ApiLog.findOne().limit(1);
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    throw error;
  }
};

/**
 * Test Redis connection by checking queue status
 */
const testRedisConnection = async () => {
  try {
    await apiLogQueue.getWaiting();
    console.log('✅ Redis connection successful');
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    throw error;
  }
};

/**
 * Set up health check functionality
 */
const setupHealthCheck = () => {
  // Log queue statistics every 5 minutes
  setInterval(async () => {
    try {
      const waiting = await apiLogQueue.getWaiting();
      const active = await apiLogQueue.getActive();
      const completed = await apiLogQueue.getCompleted();
      const failed = await apiLogQueue.getFailed();
      
      console.log(`📊 API Log Queue Stats - Waiting: ${waiting.length}, Active: ${active.length}, Completed: ${completed.length}, Failed: ${failed.length}`);
      
      // Alert if queue is backing up
      if (waiting.length > 100) {
        console.warn(`⚠️  API Log Queue backing up: ${waiting.length} jobs waiting`);
      }
      
      // Alert if too many failures
      if (failed.length > 50) {
        console.warn(`⚠️  High API Log failure rate: ${failed.length} failed jobs`);
      }
    } catch (error) {
      console.error('❌ Failed to get queue stats:', error.message);
    }
  }, 5 * 60 * 1000); // 5 minutes
};

/**
 * Get API logging system health status
 */
const getHealthStatus = async () => {
  try {
    const queueStats = {
      waiting: (await apiLogQueue.getWaiting()).length,
      active: (await apiLogQueue.getActive()).length,
      completed: (await apiLogQueue.getCompleted()).length,
      failed: (await apiLogQueue.getFailed()).length
    };
    
    const dbStats = await ApiLog.countDocuments();
    
    return {
      status: 'healthy',
      queue: queueStats,
      totalLogs: dbStats,
      worker: {
        isRunning: !apiLogWorker.closing,
        concurrency: apiLogWorker.opts.concurrency || 1
      },
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
};

/**
 * Graceful shutdown of API logging system
 */
const shutdownApiLogger = async () => {
  console.log('🛑 Shutting down API Logger...');
  
  try {
    await apiLogWorker.close();
    await apiLogQueue.close();
    console.log('✅ API Logger shutdown complete');
  } catch (error) {
    console.error('❌ Error during API Logger shutdown:', error);
  }
};

// Auto-initialize if this script is run directly
if (require.main === module) {
  initApiLogger().then((success) => {
    if (success) {
      console.log('🎉 API Logger is ready!');
      
      // Keep the process alive to monitor the worker
      process.on('SIGTERM', shutdownApiLogger);
      process.on('SIGINT', shutdownApiLogger);
      
      // Prevent the script from exiting
      setInterval(() => {
        // This keeps the process alive
      }, 1000);
    } else {
      process.exit(1);
    }
  });
}

module.exports = {
  initApiLogger,
  getHealthStatus,
  shutdownApiLogger,
  testDatabaseConnection,
  testRedisConnection
};
