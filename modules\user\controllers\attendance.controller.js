const {
  api<PERSON><PERSON><PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  Attendance,
  KGUser,
  ArazCity,
  Miqaat,
  ArazCityZone,
  Department,
} = require("../models");

const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const {
  NOT_FOUND,
  FETCH,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const haversine = require("haversine-distance");
const moment = require("moment-timezone");
const constants = require("../../../constants");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getProfile = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const user = await KGUser.findById(req.user._id)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate([
      { path: "miqaats.hierarchyPositionID", select: "name alias" },
      { path: "miqaats.departmentID", select: "name LDName" },
      { path: "miqaats.arazCityZoneID", select: "name LDName" },
      {
        path: "miqaats.arazCityID",
        select: "name LDName showPositionAlias status timeZoneID",
        populate: {
          path: "timeZoneID",
          select: "label value",
        },
      },
      {
        path: "miqaats.miqaatID",
        select: "name LDName status startDate endDate",
      },
      { path: "miqaats.kgTypeID", select: "name" },
      { path: "miqaats.kgGroupID", select: "name" },
      { path: "miqaats.functionID", select: "name" },
    ]);

  if (!user) {
    return apiError(NOT_FOUND, "User", null, res);
  }

  if (!Array.isArray(user.miqaats) || user.miqaats.length === 0) {
    return apiError(NOT_FOUND, "No Miqaats associated with user", null, res);
  }

  const matchedMiqaat = user.miqaats.find((miqaat) => {
    const matchMiqaatID = miqaatID
      ? miqaat?.miqaatID?._id?.toString() === miqaatID.toString()
      : true;
    const matchArazCityID = arazCityID
      ? miqaat?.arazCityID?._id?.toString() === arazCityID.toString()
      : true;

    return (
      matchMiqaatID &&
      matchArazCityID &&
      miqaat?.miqaatID?.status === "active" &&
      miqaat?.arazCityID?.status === true
    );
  });

  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Miqaat", null, res);
  }
  const { startDate, endDate } = matchedMiqaat.miqaatID;
  if (isEmpty(startDate) || isEmpty(endDate)) {
    return apiError(CUSTOM_ERROR, "Miqaat has Not Started", null, res);
  }
  const {
    hierarchyPositionID: hierarchyPosition,
    departmentID: department,
    arazCityZoneID: arazCityZone,
    arazCityID: arazCityRef,
    miqaatID: miqaatRef,
    kgTypeID: kgType,
    kgGroupID: kgGroup,
    functionID: functionRef,
    miqaatHR: khidmatZone,
  } = matchedMiqaat;

  const {
    _id,
    name,
    LDName,
    ITSID,
    logo: Photo,
    phone,
    whatsapp: whatsappNumber,
    gender,
  } = user;

  const userData = {
    _id,
    Name: name,
    LDName,
    ITS: ITSID,
    Photo,
    phone,
    whatsappNumber,
    gender,
    hierarchyPosition,
    department,
    khidmatZone: khidmatZone?.name,
    razaStatus: khidmatZone?.RazaStatus === "Has Raza",
    arazCityZone,
    arazCity: arazCityRef,
    miqaat: miqaatRef,
    kgType,
    kgGroup,
    function: functionRef,
    showAlias: !!arazCityRef?.showPositionAlias,
    isAttendanceAlreadyMarked: false,
  };

  const now = moment().tz(arazCityRef?.timeZoneID?.value || "UTC");
  const start = now.clone().startOf("day");
  const end = start.clone().add(24, "hours");

  const existingAttendance = await Attendance.findOne({
    userID: user._id,
    arazCityID,
    miqaatID,
    createdAt: { $gte: start.toDate(), $lt: end.toDate() },
  });

  if (
    miqaatRef?.startDate &&
    miqaatRef?.endDate &&
    !now.isBetween(miqaatRef.startDate, miqaatRef.endDate)
  ) {
    userData.isAttendanceAlreadyMarked = true;
  }

  if (existingAttendance) {
    userData.isAttendanceAlreadyMarked = true;
  }

  return apiResponse(FETCH, "User Data", userData, res);
});

const markAttendance = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, latitude, longitude } = req.body;

  const [user, arazCity, miqaat] = await Promise.all([
    KGUser.findById(req.user._id),
    ArazCity.findOne({ _id: toObjectId(arazCityID), status: true }).populate(
      "timeZoneID"
    ),
    Miqaat.findOne({ _id: toObjectId(miqaatID), status: "active" }),
  ]);

  if (!user || !arazCity || !miqaat) {
    return apiError(
      CUSTOM_ERROR,
      "Invalid user, Araz City, or Miqaat",
      null,
      res
    );
  }
  if (!arazCity.timeZoneID?.value) {
    return apiError(
      CUSTOM_ERROR,
      "Time zone not configured for Araz City",
      null,
      res
    );
  }

  const matchedMiqaat = user.miqaats?.find(
    (m) =>
      m.miqaatID.toString() === miqaatID &&
      m.arazCityID.toString() === arazCityID
  );
  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat", null, res);
  }

  const userCoords = {
    lat: parseFloat(latitude),
    lon: parseFloat(longitude),
  };

  if (matchedMiqaat.arazCityZoneID) {
    const zone = await ArazCityZone.findById(matchedMiqaat.arazCityZoneID);
    if (!zone || !zone.latitude || !zone.longitude) {
      return apiError(NOT_FOUND, "Araz City Zone Coordinates", null, res);
    }

    const zoneCoords = {
      lat: parseFloat(zone.latitude),
      lon: parseFloat(zone.longitude),
    };

    if (!haversine(userCoords, zoneCoords) <= 500) {
      return apiError(
        CUSTOM_ERROR,
        `You are not within 500m of the ${zone.name} Zone`,
        null,
        res
      );
    }
  }

  const now = moment().tz(arazCity.timeZoneID?.value || "UTC");
  const start = now.clone().startOf("day");
  const end = start.clone().add(24, "hours");

  if (!now.isBetween(start, end)) {
    return apiError(NOT_FOUND, "Attendance window has passed", null, res);
  }

  const existingAttendance = await Attendance.findOne({
    userID: user._id,
    arazCityID,
    miqaatID,
    createdAt: { $gte: start.toDate(), $lt: end.toDate() },
  });

  if (existingAttendance) {
    return apiError(
      CUSTOM_SUCCESS,
      "Attendance already marked for today",
      null,
      res
    );
  }

  await new Attendance({
    userID: user._id,
    arazCityID,
    miqaatID,
    arazCityZoneID:
      matchedMiqaat?.arazCityZoneID?._id || matchedMiqaat.arazCityZoneID,
    departmentID:
      matchedMiqaat?.departmentID?._id || matchedMiqaat.departmentID,
    latitude: userCoords.lat,
    longitude: userCoords.lon,
  }).save();

  return apiResponse(
    CUSTOM_SUCCESS,
    "Attendance marked successfully",
    null,
    res
  );
});

const getDropdownData = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, type, arazCityZoneID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const loggedInUser = req.user;
  const matchedMiqaat = loggedInUser.miqaats.find(
    (m) =>
      m.miqaatID.toString() === miqaatID &&
      m.arazCityID.toString() === arazCityID
  );
  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Miqaat", null, res);
  }

  if (type === "ZONE") {
    if (!isEmpty(matchedMiqaat.arazCityZoneID)) {
      const arazCityZone = await ArazCityZone.findById(
        matchedMiqaat.arazCityZoneID._id || matchedMiqaat.arazCityZoneID
      )
        .select("name _id")
        .lean();
      return apiResponse(
        CUSTOM_SUCCESS,
        "Zone data fetched successfully",
        [arazCityZone],
        res
      );
    } else {
      const arazCity = await ArazCity.findById(arazCityID)
        .populate("arazCityZones", "name _id")
        .select("arazCityZones")
        .lean();
      return apiResponse(
        CUSTOM_SUCCESS,
        "Zone data fetched successfully",
        arazCity.arazCityZones,
        res
      );
    }
  }

  if (type === "DEPARTMENT") {
    if (!isEmpty(matchedMiqaat.departmentID)) {
      const departmentData = await Department.findById(
        matchedMiqaat.departmentID._id || matchedMiqaat.departmentID
      )
        .select("name _id")
        .lean();

      return apiResponse(
        CUSTOM_SUCCESS,
        "Department data fetched successfully",
        [departmentData],
        res
      );
    } else {
      const isZoneCMZ =
        arazCityZoneID === constants.ARAZ_CITY_ZONES.CMZ[0].toString();

      const arazCity = await ArazCity.findById(arazCityID)
        .populate("departments.departmentID", "name _id isZonal")
        .select("departments")
        .lean();

      const departmentList = isZoneCMZ
        ? arazCity.departments.map((dept) => dept.departmentID).filter((d) => d)
        : arazCity.departments
            .filter((dept) => dept.departmentID && dept.departmentID.isZonal)
            .map((dept) => dept.departmentID);

      const cleaned = departmentList.map(({ _id, name }) => ({ _id, name }));

      return apiResponse(
        CUSTOM_SUCCESS,
        "Department data fetched successfully",
        cleaned,
        res
      );
    }
  }
});

const getAttendanceReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    startDate,
    endDate,
    departmentID,
    arazCityZoneID,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(toObjectId(arazCityID)).populate(
    "timeZoneID"
  );
  const timeZone = arazCity?.timeZoneID?.value || "UTC";

  const localStart = moment
    .tz(startDate, "DD-MM-YYYY", timeZone)
    .startOf("day");
  const localEnd = moment.tz(endDate, "DD-MM-YYYY", timeZone).endOf("day");

  const startUTC = localStart.clone().utc();
  const endUTC = localEnd.clone().utc();

  // Generate date columns for the report
  const dateColumns = [];
  const currentDate = localStart.clone();
  while (currentDate.isSameOrBefore(localEnd, "day")) {
    dateColumns.push(currentDate.format("DD-MMM"));
    currentDate.add(1, "day");
  }

  // Build elemMatch query to avoid cross combinations
  let elemMatchConditions = {
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    hierarchyPositionID: { $ne: null },
    isActive: true,
    status: { $ne: "DECLINED" },
  };

  if (arazCityZoneID?.length > 0) {
    elemMatchConditions.arazCityZoneID = {
      $in: arazCityZoneID.map(toObjectId),
    };
  }
  if (departmentID?.length > 0) {
    elemMatchConditions.departmentID = { $in: departmentID.map(toObjectId) };
  } else if (req.user.departmentID) {
    elemMatchConditions.departmentID =
      req.user.departmentID?._id || req.user.departmentID;
  }

  const pipeline = [
    {
      $match: {
        miqaats: {
          $elemMatch: elemMatchConditions,
        },
      },
    },
    {
      $addFields: {
        matchedMiqaat: {
          $first: {
            $filter: {
              input: "$miqaats",
              as: "m",
              cond: {
                $and: [
                  { $eq: ["$$m.miqaatID", toObjectId(miqaatID)] },
                  { $eq: ["$$m.arazCityID", toObjectId(arazCityID)] },
                ],
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        localField: "matchedMiqaat.hierarchyPositionID",
        foreignField: "_id",
        as: "hierarchyPosition",
      },
    },
    {
      $project: {
        _id: 1,
        name: 1,
        ITSID: 1,
        hierarchyPosition: { $arrayElemAt: ["$hierarchyPosition", 0] },
        matchedMiqaat: 1,
      },
    },
  ];

  const users = await KGUser.aggregate(pipeline);

  // Get attendance records for the date range with proper filtering
  let attendanceQuery = {
    arazCityID: toObjectId(arazCityID),
    miqaatID: toObjectId(miqaatID),
    createdAt: { $gte: startUTC.toDate(), $lte: endUTC.toDate() },
    userID: { $in: users.map((user) => user._id) }, // Only get attendance for filtered users
  };

  if (arazCityZoneID?.length > 0) {
    attendanceQuery.arazCityZoneID = { $in: arazCityZoneID.map(toObjectId) };
  }
  if (departmentID?.length > 0) {
    attendanceQuery.departmentID = { $in: departmentID.map(toObjectId) };
  } else if (req.user.departmentID) {
    attendanceQuery.departmentID =
      req.user.departmentID?._id || req.user.departmentID;
  }

  const attendance = await Attendance.find(attendanceQuery).lean();

  // Create attendance lookup map
  const attendanceMap = new Map();
  attendance.forEach((record) => {
    // Convert UTC createdAt to local timezone for proper date matching
    const localDate = moment(record.createdAt).tz(timeZone);
    const dateKey = localDate.format("DD-MMM");
    const userKey = `${record.userID.toString()}_${dateKey}`;
    attendanceMap.set(userKey, true);
  });

  // Build the report data
  const reportData = users.map((user) => {
    const row = {
      Name: user.name,
      "ITS ID": user.ITSID,
      "Hierarchy Position": user.hierarchyPosition
        ? `${user.hierarchyPosition.name}${
            user.hierarchyPosition.alias
              ? `, ${user.hierarchyPosition.alias}`
              : ""
          }`
        : "",
    };

    // Add date columns with attendance status
    dateColumns.forEach((dateCol) => {
      const userKey = `${user._id.toString()}_${dateCol}`;
      row[dateCol] = attendanceMap.has(userKey);
    });

    return row;
  });

  // Prepare response with metadata
  const response = {
    columns: ["Name", "ITS ID", "Hierarchy Position", ...dateColumns],
    data: reportData,
  };

  return apiResponse(FETCH, "Attendance Report", response, res);
});
module.exports = {
  getProfile,
  markAttendance,
  getDropdownData,
  getAttendanceReport,
};
