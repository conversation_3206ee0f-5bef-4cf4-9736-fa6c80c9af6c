const {
  api<PERSON><PERSON><PERSON>,
  apiError,
  apiResponse,
} = require("../../../utils/api.util");
const {
  Attendance,
  KGUser,
  ArazCity,
  Miqaat,
  ArazCityZone,
  Department,
} = require("../models");

const { toObjectId, isEmpty } = require("../../../utils/misc.util");
const {
  NOT_FOUND,
  FETCH,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const haversine = require("haversine-distance");
const moment = require("moment-timezone");
const constants = require("../../../constants");
const {
  isActiveMiqaatAndArazCity,
} = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getProfile = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID } = req.body;

  const user = await KGUser.findById(req.user._id)
    .select(
      "_id name ITSID miqaats logo phone whatsapp gender LDName systemRoleID"
    )
    .populate([
      { path: "miqaats.hierarchyPositionID", select: "name alias" },
      { path: "miqaats.departmentID", select: "name LDName" },
      { path: "miqaats.arazCityZoneID", select: "name LDName" },
      {
        path: "miqaats.arazCityID",
        select: "name LDName showPositionAlias status",
      },
      { path: "miqaats.miqaatID", select: "name LDName status" },
      { path: "miqaats.kgTypeID", select: "name" },
      { path: "miqaats.kgGroupID", select: "name" },
      { path: "miqaats.functionID", select: "name" },
    ]);

  if (!user) {
    return apiError(NOT_FOUND, "User not found", null, res);
  }

  if (!Array.isArray(user.miqaats) || user.miqaats.length === 0) {
    return apiError(NOT_FOUND, "No Miqaats associated with user", null, res);
  }

  const matchedMiqaat = user.miqaats.find((miqaat) => {
    const matchMiqaatID = miqaatID
      ? miqaat?.miqaatID?._id?.toString() === miqaatID.toString()
      : true;
    const matchArazCityID = arazCityID
      ? miqaat?.arazCityID?._id?.toString() === arazCityID.toString()
      : true;

    return (
      matchMiqaatID &&
      matchArazCityID &&
      miqaat?.miqaatID?.status === "active" &&
      miqaat?.arazCityID?.status === true
    );
  });

  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat not found", null, res);
  }

  const {
    hierarchyPositionID: hierarchyPosition,
    departmentID: department,
    arazCityZoneID: arazCityZone,
    arazCityID: arazCityRef,
    miqaatID: miqaatRef,
    kgTypeID: kgType,
    kgGroupID: kgGroup,
    functionID: functionRef,
    miqaatHR: khidmatZone,
  } = matchedMiqaat;

  const {
    _id,
    name,
    LDName,
    ITSID,
    logo: Photo,
    phone,
    whatsapp: whatsappNumber,
    gender,
  } = user;

  const userData = {
    _id,
    Name: name,
    LDName,
    ITS: ITSID,
    Photo,
    phone,
    whatsappNumber,
    gender,
    hierarchyPosition,
    department,
    khidmatZone: khidmatZone?.name,
    razaStatus: khidmatZone?.RazaStatus === "Has Raza",
    arazCityZone,
    arazCity: arazCityRef,
    miqaat: miqaatRef,
    kgType,
    kgGroup,
    function: functionRef,
    showAlias: !!arazCityRef?.showPositionAlias,
    isAttendanceAlreadyMarked: false,
  };

  return apiResponse(FETCH, "User Data", userData, res);
});

const markAttendance = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, latitude, longitude } = req.body;

  const [user, arazCity, miqaat] = await Promise.all([
    KGUser.findById(req.user._id),
    ArazCity.findOne({ _id: toObjectId(arazCityID), status: true }).populate(
      "timeZoneID"
    ),
    Miqaat.findOne({ _id: toObjectId(miqaatID), status: "active" }),
  ]);

  if (!user || !arazCity || !miqaat) {
    return apiError(NOT_FOUND, "Invalid user, Araz City, or Miqaat", null, res);
  }
  if (!arazCity.timeZoneID?.value) {
    return apiError(
      CUSTOM_ERROR,
      "Time zone not configured for Araz City",
      null,
      res
    );
  }

  const matchedMiqaat = user.miqaats?.find(
    (m) =>
      m.miqaatID.toString() === miqaatID &&
      m.arazCityID.toString() === arazCityID
  );
  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat not found", null, res);
  }

  const userCoords = {
    lat: parseFloat(latitude),
    lon: parseFloat(longitude),
  };

  if (matchedMiqaat.arazCityZoneID) {
    const zone = await ArazCityZone.findById(matchedMiqaat.arazCityZoneID);
    if (!zone || !zone.latitude || !zone.longitude) {
      return apiError(NOT_FOUND, "Araz City Zone Coordinates", null, res);
    }

    const zoneCoords = {
      lat: parseFloat(zone.latitude),
      lon: parseFloat(zone.longitude),
    };

    if (!haversine(userCoords, zoneCoords) <= 500) {
      return apiError(
        CUSTOM_ERROR,
        `You are not within 500m of the ${zone.name} Zone`,
        null,
        res
      );
    }
  }

  const now = moment().tz(arazCity.timeZoneID?.value || "UTC");
  const start = now.clone().startOf("day");
  const end = start.clone().add(24, "hours");

  if (!now.isBetween(start, end)) {
    return apiError(NOT_FOUND, "Attendance window has passed", null, res);
  }

  const existingAttendance = await Attendance.findOne({
    userID: user._id,
    arazCityID,
    miqaatID,
    createdAt: { $gte: start.toDate(), $lt: end.toDate() },
  });

  if (existingAttendance) {
    return apiError(
      CUSTOM_SUCCESS,
      "Attendance already marked for today",
      null,
      res
    );
  }

  await new Attendance({
    userID: user._id,
    arazCityID,
    miqaatID,
    arazCityZoneID:
      matchedMiqaat?.arazCityZoneID?._id || matchedMiqaat.arazCityZoneID,
    departmentID:
      matchedMiqaat?.departmentID?._id || matchedMiqaat.departmentID,
    latitude: userCoords.lat,
    longitude: userCoords.lon,
  }).save();

  return apiResponse(
    CUSTOM_SUCCESS,
    "Attendance marked successfully",
    null,
    res
  );
});

const getDropdownData = apiHandler(async (req, res) => {
  const { miqaatID, arazCityID, type, arazCityZoneID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const loggedInUser = req.user;
  const matchedMiqaat = loggedInUser.miqaats.find(
    (m) =>
      m.miqaatID.toString() === miqaatID &&
      m.arazCityID.toString() === arazCityID
  );
  if (!matchedMiqaat) {
    return apiError(NOT_FOUND, "Matching Miqaat not found", null, res);
  }

  if (type === "ZONE") {
    if (!isEmpty(matchedMiqaat.arazCityZoneID)) {
      const arazCityZone = await ArazCityZone.findById(
        matchedMiqaat.arazCityZoneID._id || matchedMiqaat.arazCityZoneID
      )
        .select("name _id")
        .lean();
      return apiResponse(
        CUSTOM_SUCCESS,
        "Zone data fetched successfully",
        [arazCityZone],
        res
      );
    } else {
      const arazCity = await ArazCity.findById(arazCityID)
        .populate("arazCityZones", "name _id")
        .select("arazCityZones")
        .lean();
      return apiResponse(
        CUSTOM_SUCCESS,
        "Zone data fetched successfully",
        arazCity.arazCityZones,
        res
      );
    }
  }

  if (type === "DEPARTMENT") {
    if (!isEmpty(matchedMiqaat.departmentID)) {
      const departmentData = await Department.findById(
        matchedMiqaat.departmentID._id || matchedMiqaat.departmentID
      )
        .select("name _id")
        .lean();

      return apiResponse(
        CUSTOM_SUCCESS,
        "Department data fetched successfully",
        [departmentData],
        res
      );
    } else {
      const isZoneCMZ =
        arazCityZoneID === constants.ARAZ_CITY_ZONES.CMZ[0].toString();

      const arazCity = await ArazCity.findById(arazCityID)
        .populate("departments.departmentID", "name _id isZonal")
        .select("departments")
        .lean();

      const departmentList = isZoneCMZ
        ? arazCity.departments.map((dept) => dept.departmentID).filter((d) => d)
        : arazCity.departments
            .filter((dept) => dept.departmentID && dept.departmentID.isZonal)
            .map((dept) => dept.departmentID);

      const cleaned = departmentList.map(({ _id, name }) => ({ _id, name }));

      return apiResponse(
        CUSTOM_SUCCESS,
        "Department data fetched successfully",
        cleaned,
        res
      );
    }
  }
});

const getAttendanceReport = apiHandler(async (req, res) => {
  const {
    miqaatID,
    arazCityID,
    startDate,
    endDate,
    departmentID,
    arazCityZoneID,
  } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(
    miqaatID,
    arazCityID,
    req
  );
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  const arazCity = await ArazCity.findById(toObjectId(arazCityID)).populate(
    "timeZoneID"
  );
  const timeZone = arazCity?.timeZoneID?.value || "UTC";

  const localStart = moment
    .tz(startDate, "DD-MM-YYYY", timeZone)
    .startOf("day");
  const localEnd = moment.tz(endDate, "DD-MM-YYYY", timeZone).endOf("day");

  const startUTC = localStart.clone().utc();
  const endUTC = localEnd.clone().utc();

  let query = {
    arazCityID: toObjectId(arazCityID),
    miqaatID: toObjectId(miqaatID),
    createdAt: { $gte: startUTC.toDate(), $lte: endUTC.toDate() },
  };

  if (arazCityZoneID?.length > 0) {
    query.arazCityZoneID = { $in: arazCityZoneID.map(toObjectId) };
  }
  if (departmentID?.length > 0) {
    query.departmentID = { $in: departmentID.map(toObjectId) };
  }else{
    query.departmentID = req.user.departmentID?._id || req.user.departmentID;
  }

  const attendance = await Attendance.aggregate([
    {
      $match: {
        arazCityID: toObjectId(arazCityID),
        miqaatID: toObjectId(miqaatID),
        createdAt: { $gte: startUTC.toDate(), $lte: endUTC.toDate() },
        ...(arazCityZoneID?.length && {
          arazCityZoneID: { $in: arazCityZoneID.map(toObjectId) },
        }),
        ...(departmentID?.length && {
          departmentID: { $in: departmentID.map(toObjectId) },
        }),
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "userID",
        foreignField: "_id",
        as: "user",
      },
    },
    { $unwind: "$user" },
    {
      $addFields: {
        filteredMiqaat: {
          $first: {
            $filter: {
              input: "$user.miqaats",
              as: "m",
              cond: {
                $and: [
                  { $eq: ["$$m.miqaatID", toObjectId(miqaatID)] },
                  { $eq: ["$$m.arazCityID", toObjectId(arazCityID)] },
                ],
              },
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: "hierarchypositions",
        let: { id: "$filteredMiqaat.hierarchyPositionID" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$id"] } } },
          { $project: { _id: 1, name: 1, alias: 1 } },
        ],
        as: "hierarchyPosition",
      },
    },
    {
      $lookup: {
        from: "departments",
        let: { id: "$filteredMiqaat.departmentID" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$id"] } } },
          { $project: { _id: 1, name: 1, LDName: 1 } },
        ],
        as: "department",
      },
    },
    {
      $lookup: {
        from: "kggroups",
        let: { id: "$filteredMiqaat.kgGroupID" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$id"] } } },
          { $project: { name: 1 } },
        ],
        as: "kgGroup",
      },
    },
    {
      $lookup: {
        from: "kgtypes",
        let: { id: "$filteredMiqaat.kgTypeID" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$id"] } } },
          { $project: { name: 1 } },
        ],
        as: "kgType",
      },
    },
    {
      $lookup: {
        from: "arazcityzones",
        let: { id: "$filteredMiqaat.arazCityZoneID" },
        pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$id"] } } }],
        as: "arazCityZone",
      },
    },
    {
      $lookup: {
        from: "functions",
        let: { id: "$filteredMiqaat.functionID" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$id"] } } },
          { $project: { name: 1 } },
        ],
        as: "functionDetails",
      },
    },
    {
      $project: {
        createdAt: 1,
        latitude: 1,
        longitude: 1,
        "user._id": 1,
        "user.name": 1,
        "user.LDName": 1,
        "user.email": 1,
        "user.phone": 1,
        "user.ITSID": 1,
        "user.logo": 1,

        "user.hierarchyPosition": {
          $ifNull: [{ $arrayElemAt: ["$hierarchyPosition", 0] }, null],
        },
        "user.kgType": {
          $ifNull: [{ $arrayElemAt: ["$kgType", 0] }, null],
        },
        "user.department": {
          $ifNull: [{ $arrayElemAt: ["$department", 0] }, null],
        },
        "user.arazCityZone": {
          $ifNull: [{ $arrayElemAt: ["$arazCityZone", 0] }, null],
        },
        "user.kgGroup": {
          $ifNull: [{ $arrayElemAt: ["$kgGroup", 0] }, null],
        },
        "user.functionDetails": {
          $ifNull: [{ $arrayElemAt: ["$functionDetails", 0] }, null],
        },
      },
    },
  ]);

  return apiResponse(FETCH, "Attendance Report", attendance, res);
});

module.exports = {
  getProfile,
  markAttendance,
  getDropdownData,
  getAttendanceReport,
};
