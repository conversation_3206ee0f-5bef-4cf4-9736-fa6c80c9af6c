const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
} = require("../../../utils/validator.util");

const addMiqaatSchema = Joi.object({
  name: stringValidation,
  LDName: stringValidation.allow("").optional(),
  type: stringValidation.allow("").optional(),
  status: stringValidation,
  description: stringValidation.allow("").optional(),
}).unknown();

const getSingleMiqaatSchema = Joi.object({
  id: idValidation,
});

const editMiqaatSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
  LDName: stringValidation.allow("").optional(),
  type: stringValidation.allow("").optional(),
  status: stringValidation,
  description: stringValidation.allow("").optional(),
}).unknown();

const deleteMiqaatSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addMiqaatSchema,
  getSingleMiqaatSchema,
  editMiqaatSchema,
  deleteMiqaatSchema,
};
