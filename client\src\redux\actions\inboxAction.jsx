import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest, putRequest } from 'utils/axios';

import { CommunicationInboxApiUrl } from 'utils/constant';

export const getInboxURL = (endponints) => {
  return `${CommunicationInboxApiUrl}${endponints}`;
};

const getInboxMessages = (data, dispatch) => {
  return postRequest('communication', getInboxURL('get/message'), data, true, dispatch);
};

const getSingleInboxMessage = (data, dispatch) => {
  let payload = {
    arazCityID: data?.arazCityID,
    miqaatID: data?.miqaatID
  };
  return postRequest('communication', getInboxURL(`get/message/${data?.id}`), payload, true, dispatch);
};

const sendEmail = (data, dispatch) => {
  let payload = {
    arazCityID: data?.arazCityID,
    miqaatID: data?.miqaatID,
    body: data?.body,
    attachments: data?.attachments
  };

  if (data.attachments?.length > 0) payload.awsGroupID = data?.awsGroupID;

  return putRequest('communication', getInboxURL(`get/reply/${data?.id}`), payload, true, dispatch);
};

const addAttachment = (data, dispatch) => {
  return postRequest('communication', getInboxURL(`get/attachment`), data, true, dispatch, 'formdata');
};

const getInboxAttachment = (data, dispatch) => {
  return postRequest('communication', getInboxURL('get/download-url'), data, true, dispatch);
};

const getMessageRecipient = (data, dispatch) => {
  return getRequest('communication', getInboxURL(`get/message-recipients/${data.id}`), data, true, dispatch);
};

export const getInboxMessagesAction = createGenericAsyncThunk('inbox/getInboxMessagesAction', getInboxMessages, 'get', false);

export const getSingleInboxMessageAction = createGenericAsyncThunk('inbox/getSingleInboxMessageAction', getSingleInboxMessage, 'get');

export const sendEmailAction = createGenericAsyncThunk('inbox/sendEmailAction', sendEmail);

export const addAttachmentAction = createGenericAsyncThunk('inbox/addAttachmentAction', addAttachment, 'get');

export const getInboxAttachmentAction = createGenericAsyncThunk('inbox/getInboxAttachmentAction', getInboxAttachment, 'get');

export const getMessageRecipientAction = createGenericAsyncThunk('inbox/getMessageRecipientAction', getMessageRecipient, 'get');
