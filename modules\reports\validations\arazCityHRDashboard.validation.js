const Joi = require("joi");
const { idValidation, stringValidation, numberValidation, arrayIdValidation } = require("../../../utils/validator.util");

const getArazCitySchema = Joi.object({
  miqaatID: idValidation
});

const getArazCityHrDashboardReportSchema = Joi.object({
  arazCityID: idValidation,
  miqaatID: idValidation,
  compileListID: arrayIdValidation
});

const saveArazCityHrDashboardReportSchema = Joi.object({
  arazCityID: idValidation,
  departments: Joi.array().items(Joi.object({
    departmentID: idValidation.allow(null),
    requirement: numberValidation
  }))
});

module.exports = {
  getArazCitySchema,
  getArazCityHrDashboardReportSchema,
  saveArazCityHrDashboardReportSchema
};

