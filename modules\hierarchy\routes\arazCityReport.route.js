const { validate } = require("../../../middlewares/validation.middleware");

const { getArazCityReport } = require("../../globalMasters/controllers/arazCity.controller");

const {
  getArazCityReportSchema,
} = require("../../globalMasters/validations/arazCity.validation");

const router = require("express").Router();

router.post(
  "/get",
  validate(getArazCityReportSchema, "body"),
  getArazCityReport
);

module.exports = router;
