import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest } from 'utils/axios';
import { HierarchyKGApiUrl } from 'utils/constant';
export const getUserApiURL = (endponints) => {
  return `${HierarchyKGApiUrl}${endponints}`;
};

const getProfile = (data, dispatch) => {
  return getRequest('auth', 'auth/user/get/profile', data, true, dispatch);
};

export const getProfileAction = createGenericAsyncThunk('profile/getProfileAction', getProfile, 'get');
