const { validate } = require("../../../middlewares/validation.middleware");
const { upload } = require("../../../middlewares/multer.middleware");

const {
  parseJsonFields,
  parseDateFields,
} = require("../../../middlewares/fieldProcessors.middleware");

const {
  getAllMiqaats,
  addMiqaat,
  getSingleMiqaat,
  editMiqaat,
  deleteMiqaat,
  isActiveMiqaat,
} = require("../controllers/miqaat.controller");

const {
  addMiqaatSchema,
  getSingleMiqaatSchema,
  editMiqaatSchema,
  deleteMiqaatSchema,
} = require("../validations/miqaat.validation");

const router = require("express").Router();

router.get("/get", getAllMiqaats);

router.get("/get/is-active/:id", isActiveMiqaat);

router.post(
  "/add",
  upload("miqaats").single("logo"),
  parseJsonFields(["arazCities"]),
  parseDateFields(["fasalDate", "startDate", "endDate"]),
  validate(addMiqaatSchema, "body"),
  addMiqaat
);

router.get(
  "/get/:id",
  validate(getSingleMiqaatSchema, "params"),
  getSingleMiqaat
);

router.put(
  "/edit",
  upload("miqaats").single("logo"),
  parseJsonFields(["arazCities"]),
  parseDateFields(["fasalDate", "startDate", "endDate"]),
  validate(editMiqaatSchema, "body"),
  editMiqaat
);

router.delete(
  "/delete/:id",
  validate(deleteMiqaatSchema, "params"),
  deleteMiqaat
);

module.exports = router;

