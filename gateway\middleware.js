/**
 * Gateway Middleware
 * 
 * This module contains middleware for the API Gateway including
 * authentication, rate limiting, request forwarding, and logging
 */

const rateLimit = require('express-rate-limit');
const { createProxyMiddleware } = require('http-proxy-middleware');
const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../constants');
const { GATEWAY_CONFIG, PUBLIC_ROUTES, EXCLUDED_FROM_LOGGING } = require('./config');

/**
 * Rate limiting middleware
 */
const rateLimiter = rateLimit({
  windowMs: GATEWAY_CONFIG.rateLimit.windowMs,
  max: GATEWAY_CONFIG.rateLimit.max,
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(GATEWAY_CONFIG.rateLimit.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path.includes('/health') || req.path.includes('/ping');
  }
});

/**
 * Gateway authentication middleware
 */
const gatewayAuth = (req, res, next) => {
  // Skip authentication for public routes
  const isPublicRoute = PUBLIC_ROUTES.some(route => req.path.startsWith(route));
  if (isPublicRoute) {
    return next();
  }

  const token = req.header('Authorization');
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. No token provided.'
    });
  }

  try {
    // Verify JWT token
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    } else {
      return res.status(401).json({
        success: false,
        message: 'Authentication error'
      });
    }
  }
};

/**
 * Request logging middleware
 */
const requestLogger = (req, res, next) => {
  // Skip logging for excluded routes
  const shouldSkipLogging = EXCLUDED_FROM_LOGGING.some(route => 
    req.path.includes(route)
  );
  
  if (shouldSkipLogging) {
    return next();
  }

  const startTime = Date.now();
  const originalSend = res.send;

  // Override res.send to capture response
  res.send = function(data) {
    const duration = Date.now() - startTime;
    
    // Log the request
    console.log(`🌐 Gateway: ${req.method} ${req.originalUrl} - ${res.statusCode} (${duration}ms)`);
    
    return originalSend.call(this, data);
  };

  next();
};

/**
 * Create proxy middleware for a specific service
 */
const createServiceProxy = (serviceDiscovery) => {
  return (req, res, next) => {
    try {
      // Find the target service
      const serviceName = serviceDiscovery.findServiceByRoute(req.path);
      
      if (!serviceName) {
        return res.status(404).json({
          success: false,
          message: 'Service not found for this route'
        });
      }

      // Get service URL
      const serviceUrl = serviceDiscovery.getServiceUrl(serviceName);
      
      // Create proxy middleware
      const proxy = createProxyMiddleware({
        target: serviceUrl,
        changeOrigin: true,
        timeout: GATEWAY_CONFIG.timeout,
        proxyTimeout: GATEWAY_CONFIG.timeout,
        
        // Forward all headers
        onProxyReq: (proxyReq, req, res) => {
          // Add gateway headers
          proxyReq.setHeader('X-Gateway-Request-ID', req.id || Date.now().toString());
          proxyReq.setHeader('X-Gateway-Timestamp', new Date().toISOString());
          
          // Forward user information if available
          if (req.user) {
            proxyReq.setHeader('X-User-ID', req.user.id);
            proxyReq.setHeader('X-User-ITS-ID', req.user.itsId || '');
            proxyReq.setHeader('X-User-Name', req.user.name || '');
          }
        },
        
        // Handle proxy response
        onProxyRes: (proxyRes, req, res) => {
          // Add gateway response headers
          proxyRes.headers['X-Gateway-Service'] = serviceName;
          proxyRes.headers['X-Gateway-Timestamp'] = new Date().toISOString();
        },
        
        // Handle proxy errors
        onError: (err, req, res) => {
          console.error(`❌ Gateway proxy error for ${serviceName}:`, err.message);
          
          if (!res.headersSent) {
            res.status(503).json({
              success: false,
              message: 'Service temporarily unavailable',
              service: serviceName,
              error: process.env.NODE_ENV === 'development' ? err.message : undefined
            });
          }
        }
      });

      // Execute the proxy
      proxy(req, res, next);
      
    } catch (error) {
      console.error('❌ Gateway routing error:', error);
      
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: 'Internal gateway error'
        });
      }
    }
  };
};

/**
 * Error handling middleware
 */
const errorHandler = (err, req, res, next) => {
  console.error('❌ Gateway error:', err);

  // Don't send error response if headers already sent
  if (res.headersSent) {
    return next(err);
  }

  // Default error response
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

/**
 * CORS middleware
 */
const corsMiddleware = (req, res, next) => {
  const origin = req.headers.origin;
  
  // Allow all origins in development, specific origins in production
  if (process.env.NODE_ENV === 'development') {
    res.header('Access-Control-Allow-Origin', '*');
  } else {
    // Add your production origins here
    const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || [];
    if (allowedOrigins.includes(origin)) {
      res.header('Access-Control-Allow-Origin', origin);
    }
  }
  
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization, Permissions');
  res.header('Access-Control-Allow-Credentials', 'true');
  
  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  
  next();
};

/**
 * Request ID middleware
 */
const requestId = (req, res, next) => {
  req.id = req.headers['x-request-id'] || `gw-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  res.setHeader('X-Request-ID', req.id);
  next();
};

module.exports = {
  rateLimiter,
  gatewayAuth,
  requestLogger,
  createServiceProxy,
  errorHandler,
  corsMiddleware,
  requestId
};
