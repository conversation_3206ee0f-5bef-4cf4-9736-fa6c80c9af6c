const { api<PERSON><PERSON><PERSON>, api<PERSON><PERSON>r, apiResponse } = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  UPDATE_SUCCESS,
  DELETE_SUCCESS,
  EXISTS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { KGGroup } = require("../../hierarchy/models");
const { isEmpty, getUniqueName } = require("../../../utils/misc.util");
const { redisCacheKeys, getCache, setCache, clearCacheByPattern } = require("../../../utils/redis.cache");
const { addEventLog, EventActions, Modules } = require("../../../utils/eventLogs.util");

const getAllKGGroups = apiHandler(async (req, res) => {

  const cachekey = `${redisCacheKeys.GLOBAL_MASTER}:${redisCacheKeys.KG_GROUPS}:{all}`;
  let data = await getCache(cachekey);
  if (!isEmpty(data)) {
    return apiResponse(FETCH, "KG Groups", data, res, true);
  }

  const kgGroups = await KGGroup.find({}).sort({ _id: -1 });

  setCache(cachekey, kgGroups);

  return apiResponse(FETCH, "KG Groups", kgGroups, res);
});

const addKGGroup = apiHandler(async (req, res) => {
  const { name } = req.body;

  const uniqueName = getUniqueName(name);

  const existingKGGroup = await KGGroup.findOne({ uniqueName });
  if (!isEmpty(existingKGGroup)) {
    return apiError(
      CUSTOM_ERROR,
      "KG Group with this name already exists",
      null,
      res
    );
  }

  const newKGGroup = new KGGroup({
    name,
    uniqueName,
    createdBy: req.user._id,
  });

  await newKGGroup.save();
  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:*`);
  await clearCacheByPattern(`${redisCacheKeys.HIERARCHY}:*`);
  
  await addEventLog(
    EventActions.CREATE,
    Modules.GlobalMasters,
    "KG Group",
    req.user._id
  );

  return apiResponse(ADD_SUCCESS, "KG Group", null, res);
});

const getSingleKGGroup = apiHandler(async (req, res) => {
  const { id } = req.params;

  const kgGroupData = await KGGroup.findOne({ _id: id });

  if (isEmpty(kgGroupData)) {
    return apiError(NOT_FOUND, "KG Group", null, res);
  }
  return apiResponse(FETCH, "KG Group", kgGroupData, res);
});

const editKGGroup = apiHandler(async (req, res) => {
  const { id, name } = req.body;
  
  const uniqueName = getUniqueName(name);
  
  const existingKGGroup = await KGGroup.findOne({
    uniqueName,
    _id: { $ne: id },
  });
  
  if (!isEmpty(existingKGGroup)) {
    return apiError(
      CUSTOM_ERROR,
      "KG Group with this name already exists",
      null,
      res
    );
  }

  const updateData = {
    name,
    uniqueName,
    updatedBy: req.user._id,
  };

  const kgGroupData = await KGGroup.findByIdAndUpdate(id, updateData, {
    new: true,
    runValidators: true,
  });

  if (isEmpty(kgGroupData)) {
    return apiError(NOT_FOUND, "KG Group", null, res);
  }
  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:*`);
  await clearCacheByPattern(`${redisCacheKeys.HIERARCHY}:*`);
  
  await addEventLog(
    EventActions.UPDATE,
    Modules.GlobalMasters,
    "KG Group",
    req.user._id
  );

  return apiResponse(UPDATE_SUCCESS, "KG Group", kgGroupData, res);
});

const deleteKGGroup = apiHandler(async (req, res) => {
  const { id } = req.params;
  
  const kgGroupData = await KGGroup.findOneAndDelete({ _id: id });

  if (isEmpty(kgGroupData)) {
    return apiError(NOT_FOUND, "KG Group", null, res);
  }
  
  await addEventLog(
    EventActions.DELETE,
    Modules.GlobalMasters,
    "KG Group",
    req.user._id
  );

  await clearCacheByPattern(`${redisCacheKeys.GLOBAL_MASTER}:*`);
  await clearCacheByPattern(`${redisCacheKeys.HIERARCHY}:*`);
  return apiResponse(DELETE_SUCCESS, "KG Group", null, res);
});

module.exports = {
  getAllKGGroups,
  addKGGroup,
  getSingleKGGroup,
  editKGGroup,
  deleteKGGroup,
};
