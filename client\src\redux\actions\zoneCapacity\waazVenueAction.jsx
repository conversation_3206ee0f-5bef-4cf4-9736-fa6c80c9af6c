import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityWaazVenueApiUrl } from 'utils/constant';

const getWaazVenueURL = (endponints) => {
  return `${ZoneCapacityWaazVenueApiUrl}${endponints}`;
};

const getAllWaazVenue = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL('get'), data, true, dispatch);
};

const getSingleWaazVenue = (data, dispatch) => {
  return getRequest('zonesCapacity', getWaazVenueURL(`get/${data}`), data, true, dispatch);
};

const addWaazVenue = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL('add'), data, true, dispatch);
};

const updateWaazVenue = (data, dispatch) => {
  return putRequest('zonesCapacity', getWaazVenueURL('edit'), data, true, dispatch);
};

const deleteWaazVenue = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getWaazVenueURL(`delete/${data}`), data, true, dispatch);
};

const addFinalCapacity = (data, dispatch) => {
  return patchRequest('zonesCapacity', getWaazVenueURL('final-capacity'), data, true, dispatch);
};

// Drawing Files
const getWaazDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL(`upload/${data?.functionName}/get`), data, true, dispatch);
};

const getWaazMasterDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL(`upload/file/get-master-files`), data, true, dispatch);
};

const uploadWaazDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL(`upload/newFile`), data, true, dispatch, 'formdata');
};

const viewWaazDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getWaazVenueURL(`get/download-url`), data, true, dispatch);
};

const updateWaazDrwaingFileStatus = (data, dispatch) => {
  return putRequest('zonesCapacity', getWaazVenueURL(`approve/${data?.functionName}`), data, true, dispatch);
};

const deleteWaazDrawingFile = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getWaazVenueURL(`upload/${data?.functionName}/delete/${data?.fileId}`), data, true, dispatch);
};

const activateWaazVenue = (data, dispatch) => {
  return patchRequest('zonesCapacity', getWaazVenueURL(`approve-waaz-venue`), data, true, dispatch);
};

export const getAllWaazVenueAction = createGenericAsyncThunk('waazVenue/getAllWaazVenueAction', getAllWaazVenue, 'get');

export const getSingleWaazVenueAction = createGenericAsyncThunk('waazVenue/getSingleWaazVenueAction', getSingleWaazVenue, 'get');

export const addWaazVenueAction = createGenericAsyncThunk('waazVenue/addWaazVenueAction', addWaazVenue);

export const updateWaazVenueAction = createGenericAsyncThunk('waazVenue/updateWaazVenueAction', updateWaazVenue);

export const deleteWaazVenueAction = createGenericAsyncThunk('waazVenue/deleteWaazVenueAction', deleteWaazVenue);

export const activateWaazVenueAction = createGenericAsyncThunk('waazVenue/activateWaazVenueAction', activateWaazVenue);

export const addFinalCapacityAction = createGenericAsyncThunk('waazVenue/addFinalCapacityAction', addFinalCapacity);

// Drawing Files
export const getWaazDrawingFileAction = createGenericAsyncThunk('waazVenue/getDrawingFileAction', getWaazDrawingFile);

export const getWaazMasterDrawingFileAction = createGenericAsyncThunk('waazVenue/getMasterDrawingFileAction', getWaazMasterDrawingFile);

export const uploadWaazDrawingFileAction = createGenericAsyncThunk('waazVenue/uploadWaazDrawingFileAction', uploadWaazDrawingFile);

export const viewWaazDrawingileAction = createGenericAsyncThunk('waazVenue/viewWaazDrawingFile', viewWaazDrawingFile, 'get');

export const updateWaazDrwaingFileStatusAction = createGenericAsyncThunk(
  'waazVenue/updateWaazDrwaingFileStatusAction',
  updateWaazDrwaingFileStatus
);

export const deleteWaazDrwaingFileAction = createGenericAsyncThunk('waazVenue/deleteDrwaingFileAction', deleteWaazDrawingFile);
