/**
 * API Gateway Configuration
 * 
 * This file contains the configuration for routing requests to different microservices
 */

const {
  HIERARCHY_PORT,
  COMMUNICATION_PORT,
  ZONES_CAPACITY_PORT,
  TASK_MANAGEMENT_PORT,
  SURVEY_PORT,
  AUTH_SERVICE_PORT,
  GL<PERSON>BAL_PORT
} = require('../constants');

// Default ports if environment variables are not set
const DEFAULT_PORTS = {
  HIERARCHY_PORT: 3001,
  COMMUNICATION_PORT: 3002,
  ZONES_CAPACITY_PORT: 3003,
  TASK_MANAGEMENT_PORT: 3004,
  SURVEY_PORT: 3005,
  AUTH_SERVICE_PORT: 3006,
  GLOBAL_PORT: 3007
};

// Service configuration
const SERVICES = {
  auth: {
    name: 'Authentication Service',
    host: 'localhost',
    port: AUTH_SERVICE_PORT || DEFAULT_PORTS.AUTH_SERVICE_PORT,
    healthPath: '/health',
    routes: [
      '/api/user',
      '/api/auth'
    ]
  },
  hierarchy: {
    name: 'Hierarchy Service',
    host: 'localhost',
    port: HIERARCHY_PORT || DEFAULT_PORTS.HIERARCHY_PORT,
    healthPath: '/health',
    routes: [
      '/api/hierarchy',
      '/api/hr'
    ]
  },
  global: {
    name: 'Global Masters Service',
    host: 'localhost',
    port: GLOBAL_PORT || DEFAULT_PORTS.GLOBAL_PORT,
    healthPath: '/health',
    routes: [
      '/api/global-master',
      '/api/global'
    ]
  },
  communication: {
    name: 'Communication Service',
    host: 'localhost',
    port: COMMUNICATION_PORT || DEFAULT_PORTS.COMMUNICATION_PORT,
    healthPath: '/health',
    routes: [
      '/api/communication',
      '/api/message'
    ]
  },
  taskManagement: {
    name: 'Task Management Service',
    host: 'localhost',
    port: TASK_MANAGEMENT_PORT || DEFAULT_PORTS.TASK_MANAGEMENT_PORT,
    healthPath: '/health',
    routes: [
      '/api/task-management',
      '/api/task'
    ]
  },
  zonesCapacity: {
    name: 'Zones Capacity Service',
    host: 'localhost',
    port: ZONES_CAPACITY_PORT || DEFAULT_PORTS.ZONES_CAPACITY_PORT,
    healthPath: '/health',
    routes: [
      '/api/zones-capacity',
      '/api/zones'
    ]
  },
  survey: {
    name: 'Survey Service',
    host: 'localhost',
    port: SURVEY_PORT || DEFAULT_PORTS.SURVEY_PORT,
    healthPath: '/health',
    routes: [
      '/api/survey'
    ]
  }
};

// Gateway configuration
const GATEWAY_CONFIG = {
  port: 8000,
  timeout: 30000, // 30 seconds
  retries: 3,
  healthCheckInterval: 30000, // 30 seconds
  loadBalancing: 'round-robin', // round-robin, least-connections, random
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000 // limit each IP to 1000 requests per windowMs
  }
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/v1',
  '/api/webhook',
  '/gateway/health',
  '/gateway/status',
  '/gateway/services'
];

// Routes that should be excluded from logging
const EXCLUDED_FROM_LOGGING = [
  '/gateway/health',
  '/gateway/status',
  '/health',
  '/ping',
  '/favicon.ico'
];

module.exports = {
  SERVICES,
  GATEWAY_CONFIG,
  PUBLIC_ROUTES,
  EXCLUDED_FROM_LOGGING
};
