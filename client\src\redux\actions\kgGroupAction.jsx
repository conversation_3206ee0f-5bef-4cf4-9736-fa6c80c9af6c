import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyKGGroupApiUrl } from 'utils/constant';

const getGroupURL = (endponints) => {
  return `${HierarchyKGGroupApiUrl}${endponints}`;
};

const getAllKGGroup = (data, dispatch) => {
  return getRequest('global', getGroupURL('get'), data, true, dispatch);
};

const getSingleKGGroup = (data, dispatch) => {
  return getRequest('global', getGroupURL(`get/${data}`), data, true, dispatch);
};

const addKGGroup = (data, dispatch) => {
  return postRequest('global', getGroupURL('add'), data, true, dispatch);
};

const updateKGGroup = (data, dispatch) => {
  return putRequest('global', getGroupURL('edit'), data, true, dispatch);
};

const deleteKGGroup = (data, dispatch) => {
  return deleletRequest('global', getGroupURL(`delete/${data}`), data, true, dispatch);
};

export const getAllKGGroupAction = createGenericAsyncThunk('KG/getAllKGGroupAction', getAllKGGroup, 'get');

export const getSingleKGGroupAction = createGenericAsyncThunk('KG/getSingleKGGroupAction', getSingleKGGroup, 'get');

export const addKGGroupAction = createGenericAsyncThunk('KG/addKGGroupAction', addKGGroup);

export const updateKGGroupAction = createGenericAsyncThunk('KG/updateKGGroupAction', updateKGGroup);

export const deleteKGGroupAction = createGenericAsyncThunk('KG/deleteKGGroupAction', deleteKGGroup);
