const express = require("express");
const router = express.Router();
const { validate } = require("../../../middlewares/validation.middleware");

// Import controllers
const { getProfile, markAttendance, getDropdownData, getAttendanceReport } = require("../controllers/attendance.controller");

// Import validations
const { getProfileSchema, markAttendanceSchema, getDropdownDataSchema, getAttendanceReportSchema } = require("../validations/attendance.validation");

// Attendance routes
router.post("/get/profile", validate(getProfileSchema, "body"), getProfile);
router.post("/add", validate(markAttendanceSchema, "body"), markAttendance);
router.post("/report/dropdown-data", validate(getDropdownDataSchema, "body"), getDropdownData);
router.post("/report", validate(getAttendanceReportSchema, "body"), getAttendanceReport);

module.exports = router;
