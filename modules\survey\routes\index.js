const express = require("express");
const router = express.Router();
const questionRoute = require("./question.route");
const surveyRoute = require("./surveyForm.route");
const allQuestionRoute = require("./allQuestion.route");
const allSurveyRoute = require("./allSurveyForm.route");
const surveyResponseRoute = require("./surveyResponse.route");
const reports = require("./reports.route");
const allReports = require("./allSurveyReport.route");

router.use("/survey/question", questionRoute);
router.use("/survey/all-question", allQuestionRoute);
router.use("/survey/survey-forms", surveyRoute);
router.use("/survey/all-survey-forms", allSurveyRoute);
router.use("/survey/survey-response", surveyResponseRoute);
router.use("/survey/reports", reports);
router.use("/survey/all-reports", allReports);

module.exports = router;

