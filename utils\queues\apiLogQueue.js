const { Queue, Worker } = require("bullmq");
const { connection } = require('./redis');
const { ApiLog } = require('../../modules/globalMasters/models/apiLog.model');

// Create BullMQ Queue for API Logging
const queueName = "apiLogQueue";
const apiLogQueue = new Queue(queueName, {
  connection,
  defaultJobOptions: { 
    removeOnComplete: 100, // Keep last 100 completed jobs
    removeOnFail: 50,      // Keep last 50 failed jobs
    attempts: 3,           // Retry failed jobs up to 3 times
    backoff: {
      type: 'exponential',
      delay: 2000,
    },
  },
});

/**
 * Add API log entry to the queue for asynchronous processing
 * @param {Object} logData - The API log data to be saved
 */
const queueApiLog = async (logData) => {
  try {
    await apiLogQueue.add(
      "saveApiLog",
      logData,
      {
        priority: 5, // Lower priority than critical operations
        delay: 0,    // Process immediately but asynchronously
      }
    );
  } catch (error) {
    console.error('Failed to queue API log:', error);
    // Don't throw error to avoid affecting the main API flow
  }
};

/**
 * Worker to process API log entries
 */
const apiLogWorker = new Worker(queueName, async (job) => {
  const { data: logData } = job;
  
  try {
    // Create new API log entry
    const apiLog = new ApiLog(logData);
    await apiLog.save();
    
    console.log(`API log saved: ${logData.method} ${logData.endpoint} - ${logData.responseStatus}`);
    
    return { success: true, logId: apiLog._id };
  } catch (error) {
    console.error('Failed to save API log:', error);
    throw error; // This will mark the job as failed and trigger retry
  }
}, {
  connection,
  concurrency: 5, // Process up to 5 log entries concurrently
});

// Worker event handlers
apiLogWorker.on('completed', (job) => {
  console.log(`API log job ${job.id} completed successfully`);
});

apiLogWorker.on('failed', (job, err) => {
  console.error(`API log job ${job.id} failed:`, err.message);
});

apiLogWorker.on('error', (err) => {
  console.error('API log worker error:', err);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('Shutting down API log worker...');
  await apiLogWorker.close();
  await apiLogQueue.close();
});

process.on('SIGINT', async () => {
  console.log('Shutting down API log worker...');
  await apiLogWorker.close();
  await apiLogQueue.close();
});

module.exports = {
  apiLogQueue,
  queueApiLog,
  apiLogWorker
};
