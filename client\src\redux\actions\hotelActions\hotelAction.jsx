import { createGenericAsyncThunk } from 'redux/helper';
import { AccomodationHotelPageUrl } from 'utils/constant';

const getHotelTypeURL = (endponints) => {
  return `${AccomodationHotelPageUrl}${endponints}`;
};

const getAllHotelType = (data, dispatch) => {
  return getRequest('global', getHotelTypeURL('get'), data, true, dispatch);
};

const getSingleHotelType = (data, dispatch) => {
  return getRequest('global', getHotelTypeURL(`get/${data}`), data, true, dispatch);
};

export const getAllHotelAction = createGenericAsyncThunk('Hotel/getAllHotelTypeAction', getAllHotelType, 'get');
