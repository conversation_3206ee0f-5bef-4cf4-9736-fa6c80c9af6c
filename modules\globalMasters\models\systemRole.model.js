const { Schema, model } = require("mongoose");

const permissionSchema = new Schema({
  view: { type: Boolean, default: false },
  add: { type: Boolean, default: false },
  edit: { type: Boolean, default: false },
  delete: { type: Boolean, default: false },
  report: { type: Boolean, default: false },
  "final-capacity": { type: Boolean, default: false },
  "approve-waaz-venue": { type: Boolean, default: false },
  "activate-waaz-venue": { type: Boolean, default: false },
  "activate-mawaid-venue": { type: Boolean, default: false },
  upload: { 
    type: Schema.Types.Mixed, 
    default: false,
    validate: {
      validator: function(v) {
        return typeof v === 'boolean' || 
               (typeof v === 'object' && v !== null);
      },
      message: props => `${props.value} is not a valid upload permission!`
    }
  },
  approve: { 
    type: Schema.Types.Mixed, 
    default: false,
    validate: {
      validator: function(v) {
        return typeof v === 'boolean' || 
               (typeof v === 'object' && v !== null);
      },
      message: props => `${props.value} is not a valid approve permission!`
    }
  },
  finalCapacity: { type: <PERSON>olean, default: false },
  approveWaazVenue: { type: Boolean, default: false },
  approveMasterCad: { type: Boolean, default: false }
}, { _id: false });

const defaultPermissions = {
  view: false,
  add: false,
  edit: false,
  delete: false,
  upload: false,
  approve: false
};

const optionSchema = new Schema({
  name: {
    type: String,
    required: false,
  },
  routeName: {
    type: String,
    required: false,
  },
  type: {
    type: String,
    required: false,
  },
  module: {
    type: String,
    required: false,
  },
  permissions: {
    type: permissionSchema,
    default: defaultPermissions,
    _id: false,
  }
});

const moduleSchema = new Schema({
  name: {
    type: String,
    required: true,
  },
  view: {
    type: Boolean,
    default: true,
  },
  moduleIcon: {
    type: String,
    required: false,
  },
  moduleColor: {
    type: String,
    required: false,
  },
  md: {
    type: Number,
    required: false,
  },
  options: [optionSchema],
});

const systemRoleSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    uniqueName: {
      type: String,
      required: true,
      trim: true,
      unique: true,
    },
    modules: [moduleSchema],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    isSystem: {
      type: Boolean,
      required: false,
      default: false,
    },
    isAccessible: {
      type: Boolean,
      required: false,
      default: false,
    },
    isVisibleForArazCityUser: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// systemRoleSchema.pre('save', function(next) {
//   try {
//     this.modules.forEach(module => {
//       module.options.forEach(option => {
//         const permissions = option.permissions;
//         if (typeof permissions.upload === 'boolean' && permissions.upload === true) {
//           permissions.upload = { 
//             permission: true, 
//             subPermission: [] 
//           };
//         }
//         if (typeof permissions.approve === 'boolean' && permissions.approve === true) {
//           permissions.approve = { 
//             permission: true, 
//             subPermission: [] 
//           };
//         }
//       });
//     });
//     next();
//   } catch (error) {
//     next(error);
//   }
// });

const SystemRole = model("SystemRole", systemRoleSchema);

module.exports = { SystemRole };