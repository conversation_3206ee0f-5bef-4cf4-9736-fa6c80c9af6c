const express = require("express");
const router = express.Router();

const arazCityZoneRoute = require("./arazCityZone.route");
const systemUserRoute = require("./systemUser.route");
const arazCityDepartmentRoute = require("./arazCityDepartment.route");

const jamiatJamaatRoute = require("./jamiatJamaat.route");
const hierarchyRoute = require("./hierarchy.route");
const kgUserRoute = require("./kgUser.route");
const systemRoleRoute = require("./systemRole.route");
const healthcheckRoute = require("./healthcheck.route");
const dashboardRoute = require("./dashboard.route");
const departmentReportRoute = require("./departmentReport.route");
const arazCityZoneReportRoute = require("./arazCityZoneReport.route");
const arazCityReportRoute = require("./arazCityReport.route");

const arazCityZoneFilesRoute = require("./arazCityZoneFile.route");

const recommendUserRoute = require("./recommendUser.route");
const compileUserListRoute = require("./compileUserList.route");
const razaListRoute = require("./razaList.route");
const interestRoute = require("./interest.route");
const zoneTeamReportRoute = require("./zoneTeamReport.route");
const kgRequisitionByDepartmentRoute = require("./kgRequisitionByDepartment.route");
const kgRequisitionByZoneRoute = require("./kgRequisitionByZone.route");
const approvedCompileListRoute = require("./approvedCompileList.route");
const print = require("./print.route")
const unprint = require("./unprint.route")

router.use("/hierarchy/unprint", unprint);
router.use("/hierarchy/print", print);
router.use("/hierarchy/healthcheck", healthcheckRoute);
router.use("/hierarchy/hierarchy", hierarchyRoute);
router.use("/hierarchy/dashboard", dashboardRoute);
router.use("/hierarchy/jamiat-jamaat", jamiatJamaatRoute);
router.use("/hierarchy/system-user", systemUserRoute);
router.use("/hierarchy/araz-city-department", arazCityDepartmentRoute);
router.use("/hierarchy/araz-city-zone", arazCityZoneRoute);
router.use("/hierarchy/kg-user", kgUserRoute);
router.use("/hierarchy/system-role", systemRoleRoute);
router.use("/hierarchy/department-report", departmentReportRoute);
router.use("/hierarchy/araz-city-report", arazCityReportRoute);
router.use("/hierarchy/araz-city-zone-report", arazCityZoneReportRoute);
router.use("/hierarchy/interest", interestRoute);
router.use("/hierarchy/araz-city-zone-file", arazCityZoneFilesRoute);
router.use("/hierarchy/araz-city-zone-team-report", zoneTeamReportRoute);
router.use("/hierarchy/kg-requisition-by-department", kgRequisitionByDepartmentRoute);
router.use("/hierarchy/kg-requisition-by-zone", kgRequisitionByZoneRoute);
router.use("/hierarchy/recommend-users", recommendUserRoute);
router.use("/hierarchy/compile-user-list", compileUserListRoute);
router.use("/hierarchy/approved-compile-lists",approvedCompileListRoute)
router.use("/hierarchy/raza-list", razaListRoute)



//to be remove in future
// const arazCityRoute = require("../../globalMasters/routes/arazCity.route");
// const departmentRoute = require("../../globalMasters/routes/department.route");
// const miqaatRoute = require("../../globalMasters/routes/miqaat.route");
// const kgTypeRoute = require("../../globalMasters/routes/kgType.route");
// const kgGroupRoute = require("../../globalMasters/routes/kgGroup.route");
// const functionRoute = require("../../globalMasters/routes/function.route");
// const hierarchyPositionRoute = require("../../globalMasters/routes/hierarchyPosition.route");
// const smeMappingRoute = require("../../globalMasters/routes/smeMapping.route");
// router.use("/miqaat", miqaatRoute);
// router.use("/department", departmentRoute);
// router.use("/araz-city", arazCityRoute);
// router.use("/kg-type", kgTypeRoute);
// router.use("/kg-group", kgGroupRoute);
// router.use("/function", functionRoute);
// router.use("/kg-hierarchy-position", hierarchyPositionRoute);
// router.use("/sme-mapping", smeMappingRoute);


const kgRequisitionRoute = require("./kgRequistion.route");
const kgRequisitionApplicationRoute = require("./kgRequistionApplication.route");

router.use("/kg-requisition", kgRequisitionRoute);
router.use("/kg-requisition-application", kgRequisitionApplicationRoute);


module.exports = router;
