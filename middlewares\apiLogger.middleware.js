const { logApiCall } = require("../utils/apiLogger.util");

/**
 * Middleware to log API calls asynchronously
 * This middleware captures request/response data and queues it for logging
 * without affecting the API response time
 */
const apiLoggerMiddleware = (req, res, next) => {
  // Record start time
  const startTime = Date.now();
  
  // Skip logging for certain endpoints to avoid noise
  const skipLogging = [
    '/api/healthcheck',
    '/api/health',
    '/favicon.ico',
    '/api/ping'
  ];
  
  const shouldSkip = skipLogging.some(path => 
    req.originalUrl?.includes(path) || req.url?.includes(path)
  );
  
  if (shouldSkip) {
    return next();
  }
  
  // Store original response methods
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;
  
  let responseData = null;
  let isResponseCaptured = false;
  
  // Override res.send to capture response data
  res.send = function(data) {
    if (!isResponseCaptured) {
      responseData = data;
      isResponseCaptured = true;
      
      // Log the API call asynchronously
      setImmediate(() => {
        logApiCall(req, res, startTime, responseData);
      });
    }
    
    return originalSend.call(this, data);
  };
  
  // Override res.json to capture JSON response data
  res.json = function(data) {
    if (!isResponseCaptured) {
      responseData = data;
      isResponseCaptured = true;
      
      // Log the API call asynchronously
      setImmediate(() => {
        logApiCall(req, res, startTime, responseData);
      });
    }
    
    return originalJson.call(this, data);
  };
  
  // Override res.end to capture cases where send/json aren't used
  res.end = function(data) {
    if (!isResponseCaptured) {
      responseData = data;
      isResponseCaptured = true;
      
      // Log the API call asynchronously
      setImmediate(() => {
        logApiCall(req, res, startTime, responseData);
      });
    }
    
    return originalEnd.call(this, data);
  };
  
  // Handle errors
  const originalNext = next;
  next = function(error) {
    if (error && !isResponseCaptured) {
      isResponseCaptured = true;
      
      // Log the error asynchronously
      setImmediate(() => {
        logApiCall(req, res, startTime, null, error);
      });
    }
    
    return originalNext(error);
  };
  
  // Continue to next middleware
  next();
};

/**
 * Middleware specifically for error logging
 * This should be used as an error handling middleware
 */
const apiErrorLoggerMiddleware = (error, req, res, next) => {
  const startTime = req.startTime || Date.now();
  
  // Log the error asynchronously
  setImmediate(() => {
    logApiCall(req, res, startTime, null, error);
  });
  
  // Continue with error handling
  next(error);
};

/**
 * Simple middleware to attach start time to request
 * Use this before other middlewares if you want more accurate timing
 */
const attachStartTime = (req, res, next) => {
  req.startTime = Date.now();
  next();
};

module.exports = {
  apiLoggerMiddleware,
  apiErrorLoggerMiddleware,
  attachStartTime
};
