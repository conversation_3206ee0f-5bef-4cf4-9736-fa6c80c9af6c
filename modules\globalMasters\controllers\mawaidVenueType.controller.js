const {
  apiHand<PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const {
  isEmpty,
  toObjectId,
  getUniqueName,
} = require("../../../utils/misc.util");
const { MawaidVenueType, MawaidVenue } = require("../../zonesCapacity/models");

const getAllMawaidVenueTypes = apiHandler(async (req, res) => {
  const mawaidVenueTypes = await MawaidVenueType.find().sort({ _id: -1 });
  return apiResponse(FETCH, "Mawaid Venue Types", mawaidVenueTypes, res);
});

const addEditMawaidVenueType = apiHandler(async (req, res) => {
  const { id } = req.body;
  let data = req.body;

  data.uniqueName = getUniqueName(data.name);

  const uniqueNameQueryObject = { uniqueName: data.uniqueName };

  if (id) uniqueNameQueryObject._id = { $ne: id };

  const existingMawaidVenueType = await MawaidVenueType.findOne(
    uniqueNameQueryObject
  );

  if (existingMawaidVenueType) {
    return apiError(
      CUSTOM_ERROR,
      "Mawaid Venue Type with this name already exists",
      null,
      res
    );
  }

  if (!id) {
    data.createdBy = req.user._id;

    const newMawaidVenueType = new MawaidVenueType(data);

    let savedMawaidVenueTypeData = await newMawaidVenueType.save();

    return apiResponse(
      ADD_SUCCESS,
      "Mawaid Venue Type",
      savedMawaidVenueTypeData,
      res
    );
  } else {
    data.updatedBy = req.user._id;

    let updatedMawaidVenueTypeData = await MawaidVenueType.findByIdAndUpdate(
      id,
      data,
      { new: true, runValidators: true }
    );

    if (!updatedMawaidVenueTypeData) {
      return apiError(NOT_FOUND, "Mawaid Venue Type", null, res);
    }

    return apiResponse(
      UPDATE_SUCCESS,
      "Mawaid Venue Type",
      updatedMawaidVenueTypeData,
      res
    );
  }
});

const getSingleMawaidVenueType = apiHandler(async (req, res) => {
  const { id } = req.params;
  const mawaidVenueTypeData = await MawaidVenueType.findById(id);

  if (!mawaidVenueTypeData)
    return apiError(NOT_FOUND, "Mawaid Venue Type", null, res);

  return apiResponse(FETCH, "Mawaid Venue Type", mawaidVenueTypeData, res);
});

const deleteMawaidVenueType = apiHandler(async (req, res) => {
  const { id } = req.params;

  const existsInMawaidVenue = await MawaidVenue.exists({
    mawaidVenueTypeID: id,
  });

  if (existsInMawaidVenue) {
    return apiError(
      CUSTOM_ERROR,
      "Couldn't Delete Mawaid Venue Type as present in a Mawaid Venue",
      null,
      res
    );
  }

  const mawaidVenueTypeData = await MawaidVenueType.findOneAndDelete({
    _id: id,
  });

  if (!mawaidVenueTypeData)
    return apiError(NOT_FOUND, "Mawaid Venue Type", null, res);

  return apiResponse(DELETE_SUCCESS, "Mawaid Venue Type", null, res);
});

module.exports = {
  getAllMawaidVenueTypes,
  addEditMawaidVenueType,
  getSingleMawaidVenueType,
  deleteMawaidVenueType,
};
