import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import {
  AllQuestionFormApiUrl,
  AllSurveyFormApiUrl,
  QuestionnaireFormApiUrl,
  ResponseSurveyFormApiUrl,
  SurveyFormApiUrl
} from 'utils/constant';

const getQuestionApiUrl = (endponints) => {
  return `${QuestionnaireFormApiUrl}${endponints}`;
};

const getAllQuestionApiUrl = (endponints) => {
  return `${AllQuestionFormApiUrl}${endponints}`;
};

const getSurveyApiUrl = (endponints) => {
  return `${SurveyFormApiUrl}${endponints}`;
};

const getResSurveyApiUrl = (endponints) => {
  return `${ResponseSurveyFormApiUrl}${endponints}`;
};

const getAllQuestions = (data, dispatch) => {
  return getRequest('survey', `${AllQuestionFormApiUrl}get`, data, true, dispatch);
};

const getMyQuestions = (data, dispatch) => {
  return getRequest('survey', getQuestionApiUrl('get'), data, true, dispatch);
};

const getSingleQuestion = (data, dispatch) => {
  return getRequest('survey', getQuestionApiUrl(`get/${data}`), data, true, dispatch);
};

const getAllSingleQuestion = (data, dispatch) => {
  return getRequest('survey', getAllQuestionApiUrl(`get/${data}`), data, true, dispatch);
};

const addQuestion = (data, dispatch) => {
  return postRequest('survey', getQuestionApiUrl('add'), data, true, dispatch);
};

const updateQuestion = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return putRequest('survey', getQuestionApiUrl(`edit/${id}`), payload, true, dispatch);
};

const updateAllQuestion = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return putRequest('survey', getAllQuestionApiUrl(`edit/${id}`), payload, true, dispatch);
};

const uploadSurveyFile = (data, dispatch) => {
  let { id, formData, ...rest } = data;
  let payload = { ...rest };
  return postRequest('survey', getResSurveyApiUrl(`get/upload/${id}`), formData, true, dispatch, 'formdata');
};

const getSurveyFile = (data, dispatch) => {
  return postRequest('survey', '/survey/reports/get/survey/file', data, true, dispatch);
};

const getAllSurveyReportFile = (data, dispatch) => {
  return postRequest('survey', `${AllSurveyFormApiUrl}/get/survey/file`, data, true, dispatch);
};

const deleteQuestion = (data, dispatch) => {
  return deleletRequest('survey', getQuestionApiUrl(`delete/${data}`), data, true, dispatch);
};

const deleteAllQuestion = (data, dispatch) => {
  return deleletRequest('survey', getAllQuestionApiUrl(`delete/${data}`), data, true, dispatch);
};

const getAllSurvey = (data, dispatch) => {
  return getRequest('survey', `${AllSurveyFormApiUrl}get`, data, true, dispatch);
};

const getSurveyReport = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return postRequest('survey', `survey/reports/get/${id}`, payload, true, dispatch);
};

const getSingleSurveyReport = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return postRequest('survey', `${AllSurveyFormApiUrl}report/${id}`, payload, true, dispatch);
};

const getSingleAllSurvey = (data, dispatch) => {
  return getRequest('survey', `${AllSurveyFormApiUrl}get/${data}`, data, true, dispatch);
};
const getAllWithoutMemberSurvey = (data, dispatch) => {
  return getRequest('survey', `${AllSurveyFormApiUrl}get/without-members/${data}`, data, true, dispatch);
};

const getAllSurveyFile = (data, dispatch) => {
  return postRequest('survey', `survey/all-reports/get/survey/file`, data, true, dispatch);
};

const updateSingleAllSurvey = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return putRequest('survey', `${AllSurveyFormApiUrl}edit/${id}`, payload, true, dispatch);
};

const deleteSingleSurvey = (data, dispatch) => {
  return deleletRequest('survey', `${AllSurveyFormApiUrl}delete/${data}`, data, true, dispatch);
};

const getAllSurveyReports = (data, dispatch) => {
  return getRequest('survey', `survey/reports/get`, data, true, dispatch);
};

const getAllReports = (data, dispatch) => {
  return postRequest('survey', `survey/all-reports/get`, data, true, dispatch);
};

const getSingleReports = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return postRequest('survey', `survey/all-reports/get/${id}`, payload, true, dispatch);
};

const getMySurvey = (data, dispatch) => {
  return getRequest('survey', getSurveyApiUrl('get'), data, true, dispatch);
};

const checkIsThisQuestionAlreadyMapped = (data, dispatch) => {
  return postRequest('survey', getSurveyApiUrl(`get/check-question-used`), data, true, dispatch);
};

const getSimilarQuestions = (data, dispatch) => {
  return postRequest('survey', getQuestionApiUrl('get/similar'), data, true, dispatch);
};

const getAllQuestionsForSurvey = (data, dispatch) => {
  return getRequest('survey', getSurveyApiUrl('add/get-questions'), data, true, dispatch);
};

const getSurveyToAttend = (data, dispatch) => {
  return getRequest('survey', getResSurveyApiUrl('get/surveys'), data, true, dispatch);
};

const getSingleSurveyToAttend = (data, dispatch) => {
  return getRequest('survey', getResSurveyApiUrl(`get/${data}`), data, true, dispatch);
};

const addResponse = (data, dispatch) => {
  return postRequest('survey', getResSurveyApiUrl(`get/add`), data, true, dispatch);
};

const getSingleSurvey = (data, dispatch) => {
  return getRequest('survey', getSurveyApiUrl(`get/${data}`), data, true, dispatch);
};

const getSurvey = (data, dispatch) => {
  return getRequest('survey', getSurveyApiUrl(`get/without-members/${data}`), data, true, dispatch);
};

const addSurvey = (data, dispatch) => {
  return postRequest('survey', getSurveyApiUrl('add'), data, true, dispatch);
};

const updateSurvey = (data, dispatch) => {
  let { id, ...rest } = data;
  let payload = { ...rest };
  return putRequest('survey', getSurveyApiUrl(`edit/${id}`), payload, true, dispatch);
};

const deleteSurvey = (data, dispatch) => {
  return deleletRequest('survey', getSurveyApiUrl(`delete/${data}`), data, true, dispatch);
};

const updateSurveyStatus = (data, dispatch) => {
  return postRequest('survey', getSurveyApiUrl(`add/activate/${data?.id}`), data, true, dispatch);
};

const updateQuestionStatus = (data, dispatch) => {
  return postRequest('survey', getQuestionApiUrl(`add/activate/${data?.id}`), data, true, dispatch);
};

export const getAllQuestionsAction = createGenericAsyncThunk('survey/getAllQuestionsAction', getAllQuestions, 'get');
export const getAllQuestionsForSurveyAction = createGenericAsyncThunk(
  'survey/getAllQuestionsForSurveyAction',
  getAllQuestionsForSurvey,
  'get'
);
export const updateAllQuestionsAction = createGenericAsyncThunk('survey/updateAllQuestionsAction', updateAllQuestion);
export const getAllSingleQuestionAction = createGenericAsyncThunk('survey/getAllSingleQuestionAction', getAllSingleQuestion, 'get');
export const deleteAllQuestionAction = createGenericAsyncThunk('survey/deleteAllQuestionAction', deleteAllQuestion);

// My Questions
export const getMyQuestionsAction = createGenericAsyncThunk('survey/getMyQuestionsAction', getMyQuestions, 'get');
export const getSingleQuestionAction = createGenericAsyncThunk('survey/getSingleQuestionAction', getSingleQuestion, 'get');
export const addQuestionsAction = createGenericAsyncThunk('survey/addQuestionsAction', addQuestion);
export const updateQuestionsAction = createGenericAsyncThunk('survey/updateQuestionsAction', updateQuestion);
export const deleteQuestionAction = createGenericAsyncThunk('survey/deleteQuestionAction', deleteQuestion);
export const updateQuestionStatusAction = createGenericAsyncThunk('survey/updateQuestionStatusAction', updateQuestionStatus);

export const getAllSurveyAction = createGenericAsyncThunk('survey/getAllSurveyAction', getAllSurvey, 'get');
export const getSingleAllSurveyAction = createGenericAsyncThunk('survey/getSingleAllSurveyAction', getSingleAllSurvey, 'get');
export const getAllWithoutMemberSurveyAction = createGenericAsyncThunk(
  'survey/getAllWithoutMemberSurveyAction',
  getAllWithoutMemberSurvey,
  'get'
);
export const getAllSurveyFileAction = createGenericAsyncThunk('survey/getAllSurveyFileAction', getAllSurveyFile, 'get');
export const getAllSurveyReportFileAction = createGenericAsyncThunk('survey/getAllSurveyReportFileAction', getAllSurveyFile, 'get');
export const updateSingleAllSurveyAction = createGenericAsyncThunk('survey/updateSingleAllSurveyAction', updateSingleAllSurvey);
export const deleteSingleSurveyAction = createGenericAsyncThunk('survey/deleteSingleSurveyAction', deleteSingleSurvey);
export const getSingleSurveyReportAction = createGenericAsyncThunk('survey/getSingleSurveyReportAction', getSingleSurveyReport, 'get');

export const getMySurveyAction = createGenericAsyncThunk('survey/getMySurveyAction', getMySurvey, 'get');
export const getSurveyToAttendAction = createGenericAsyncThunk('survey/getSurveyToAttendAction', getSurveyToAttend, 'get');
export const getSingleSurveyToAttendAction = createGenericAsyncThunk(
  'survey/getSingleSurveyToAttendAction',
  getSingleSurveyToAttend,
  'get'
);
export const getSingleSurveyAction = createGenericAsyncThunk('survey/getSingleSurveyAction', getSingleSurvey, 'get');
export const getSurveyAction = createGenericAsyncThunk('survey/getSurveyAction', getSurvey, 'get');
export const addSurveyAction = createGenericAsyncThunk('survey/addSurveyAction', addSurvey);
export const updateSurveyAction = createGenericAsyncThunk('survey/updateSurveyAction', updateSurvey);
export const deleteSurveyAction = createGenericAsyncThunk('survey/deleteSurveyAction', deleteSurvey);
export const updateSurveyStatusAction = createGenericAsyncThunk('survey/updateSurveyStatusAction', updateSurveyStatus);
export const addResponseAction = createGenericAsyncThunk('survey/addResponseAction', addResponse);

// report
export const getAllSurveyReportsAction = createGenericAsyncThunk('survey/getAllSurveyReportsAction', getAllSurveyReports, 'get');
export const getAllReportsAction = createGenericAsyncThunk('survey/getAllReportsAction', getAllReports, 'get');
export const getSingleReportsAction = createGenericAsyncThunk('survey/getSingleReportsAction', getSingleReports, 'get');
export const getSurveyReportAction = createGenericAsyncThunk('survey/getSurveyReportAction', getSurveyReport, 'get');
export const uploadSurveyFileAction = createGenericAsyncThunk('survey/uploadSurveyFileAction', uploadSurveyFile);
export const getSurveyFileAction = createGenericAsyncThunk('survey/getSurveyFileAction', getSurveyFile, 'get');
//questionMapping
export const checkIsThisQuestionAlreadyMappedAction = createGenericAsyncThunk(
  'survey/checkIsThisQuestionAlreadyMappedAction',
  checkIsThisQuestionAlreadyMapped,
  'get'
);
export const getSimilarQuestionsAction = createGenericAsyncThunk('survey/getSimilarQuestionsAction', getSimilarQuestions, 'get');
