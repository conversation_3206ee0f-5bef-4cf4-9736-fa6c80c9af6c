import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { AsharaGuideApiUrl, AsharaGuideDefaultViewApiUrl } from 'utils/constant';

const getAsharaGuideURL = (endponints) => {
  return `${AsharaGuideApiUrl}${endponints}`;
};

const getAsharaGuides = (data, dispatch) => {
  return getRequest('global', getAsharaGuideURL('get/all'), data, true, dispatch);
};

const getDefaultAsharaGuides = (data, dispatch) => {
  return getRequest('global', `${AsharaGuideDefaultViewApiUrl}${'get/all'}`, data, true, dispatch);
};

const getSingleAsharaGuides = (data, dispatch) => {
  return getRequest('global', getAsharaGuideURL(`get/${data}`), data, true, dispatch);
};

const addAsharaGuide = (data, dispatch) => {
  return postRequest('global', getAsharaGuideURL('add'), data, true, dispatch);
};

const updateAsharaGuide = (data, dispatch) => {
  return putRequest('global', getAsharaGuideURL('edit'), data, true, dispatch);
};

const deleteAsharaGuide = (data, dispatch) => {
  return deleletRequest('global', getAsharaGuideURL(`delete/${data}`), data, true, dispatch);
};

const uploadFile = (data, dispatch) => {
  return postRequest('global', getAsharaGuideURL(`add/upload-file`), data, true, dispatch, 'formdata');
};

const recordUserClick = (data, dispatch) => {
  return patchRequest('global', getAsharaGuideURL(`add/click`), data, true, dispatch);
};

const userClickReport = (data, dispatch) => {
  return postRequest('global', getAsharaGuideURL(`get/user-click-report/${data}`), data, true, dispatch);
};

const getAttachment = (data, dispatch) => {
  return postRequest('global', `${AsharaGuideDefaultViewApiUrl}get/download-url`, data, true, dispatch);
};

export const getAsharaGuidesAction = createGenericAsyncThunk('asharaGuide/getAsharaGuidesAction', getAsharaGuides, 'get');

export const getDefaultAsharaGuidesAction = createGenericAsyncThunk(
  'asharaGuide/getDefaultAsharaGuidesAction',
  getDefaultAsharaGuides,
  'get'
);

export const getSingleAsharaGuidesAction = createGenericAsyncThunk('asharaGuide/getSingleAsharaGuidesAction', getSingleAsharaGuides, 'get');

export const addAsharaGuideAction = createGenericAsyncThunk('asharaGuide/addAsharaGuideAction', addAsharaGuide);

export const updateAsharaGuideAction = createGenericAsyncThunk('asharaGuide/updateAsharaGuideAction', updateAsharaGuide);

export const deleteAsharaGuideAction = createGenericAsyncThunk('asharaGuide/deleteAsharaGuideAction', deleteAsharaGuide);

export const uploadFileAction = createGenericAsyncThunk('asharaGuide/uploadFileAction', uploadFile, 'get');

export const recordUserClickAction = createGenericAsyncThunk('asharaGuide/recordUserClickAction', recordUserClick, 'get');

export const userClickReportAction = createGenericAsyncThunk('asharaGuide/userClickReportAction', userClickReport, 'get');

export const getAttachmentAction = createGenericAsyncThunk('asharaGuide/getAttachmentAction', getAttachment, 'get');
