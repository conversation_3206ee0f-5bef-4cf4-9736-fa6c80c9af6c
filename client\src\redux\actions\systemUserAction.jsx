import { get } from 'lodash';
import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { SystemUserApiUrl } from 'utils/constant';

const getSystemUserURL = (endponints) => {
  return `${SystemUserApiUrl}${endponints}`;
};

const getSystemUsers = (data, dispatch) => {
  return getRequest('hierarchy', getSystemUserURL('get'), data, true, dispatch);
};

const getSingleSystemUser = (data, dispatch) => {
  return getRequest('hierarchy', getSystemUserURL(`get/${data}`), data, true, dispatch);
};

const addSystemUser = (data, dispatch) => {
  return postRequest('hierarchy', getSystemUserURL('add'), data, true, dispatch);
};

const updateSystemUser = (data, dispatch) => {
  return putRequest('hierarchy', getSystemUserURL('edit'), data, true, dispatch);
};

const deleteSystemUser = (data, dispatch) => {
  return deleletRequest('hierarchy', getSystemUserURL(`delete/${data}`), data, true, dispatch);
};

export const getSystemUsersAction = createGenericAsyncThunk('SystemUsers/getSystemUsersAction', getSystemUsers, 'get');

export const getSingleSystemUserAction = createGenericAsyncThunk('SystemUsers/getSingleSystemUserAction', getSingleSystemUser, 'get');

export const addSystemUserAction = createGenericAsyncThunk('SystemUsers/addSystemUsersAction', addSystemUser);

export const updateSystemUserAction = createGenericAsyncThunk('SystemUsers/updateSystemUserAction', updateSystemUser);

export const deleteSystemUserAction = createGenericAsyncThunk('SystemUsers/deleteSystemUserAction', deleteSystemUser);
