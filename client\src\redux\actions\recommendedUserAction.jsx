import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest } from 'utils/axios';

const hierarchyRecommendedUserURL = (endponints) => {
  return `hierarchy/recommend-users/${endponints}`;
};

const getHierarchyRecommendedUsers = (data, dispatch) => {
  return getRequest('hierarchy', hierarchyRecommendedUserURL('get'), data, true, dispatch);
};

const getRecommendedUsersDeptQuota = (data, dispatch) => {
  return getRequest('hierarchy', hierarchyRecommendedUserURL('get/departmental-quota'), data, true, dispatch);
};

const importUsersByITS = (data, dispatch) => {
  return postRequest('hierarchy', hierarchyRecommendedUserURL('get/import-its-users'), data, true, dispatch);
};

const getRecommendedUserArazCities = (data, dispatch) => {
  return getRequest('hierarchy', `${hierarchyRecommendedUserURL('get')}/araz-cities`, data, true, dispatch);
};

const getRecommendedUserTravelPriority = (data, dispatch) => {
  return getRequest('hierarchy', `${hierarchyRecommendedUserURL('get')}/travel-priorities`, data, true, dispatch);
};

const getRecommendedUserRazaRecommendation = (data, dispatch) => {
  return getRequest('hierarchy', `${hierarchyRecommendedUserURL('get')}/raza-recommendations`, data, true, dispatch);
};

const getRecommendedUserDepartments = (data, dispatch) => {
  return getRequest('hierarchy', `${hierarchyRecommendedUserURL('get')}/departments`, data, true, dispatch);
};

const addHierarchyRecommendedUser = (data, dispatch) => {
  return patchRequest('hierarchy', hierarchyRecommendedUserURL('add'), data, true, dispatch);
};

const addCompileList = (data, dispatch) => {
  return postRequest('hierarchy', 'hierarchy/compile-user-list/add', data, true, dispatch);
};

const editHierarchyRecommendedUser = (data, dispatch) => {
  return patchRequest('hierarchy', hierarchyRecommendedUserURL('edit'), data, true, dispatch);
};

const deleteHierarchyRecommendedUser = (data, dispatch) => {
  return deleletRequest('hierarchy', `${hierarchyRecommendedUserURL(`delete/${data.id}`)}`, data, true, dispatch);
};

const getDepartmentWiseUsers = (data, dispatch) => {
  return getRequest('hierarchy', `hierarchy/compile-user-list/get/department-users/${data.id}`, data, true, dispatch);
};

export const getRecommendedUsersAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUsersAction',
  getHierarchyRecommendedUsers,
  'get',
  false
);

export const getRecommendedUsersDeptQuotaAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUsersDeptQuotaAction',
  getRecommendedUsersDeptQuota,
  'get',
  false
);

export const importUsersByITSAction = createGenericAsyncThunk('hierarchy/importUsersByITSAction', importUsersByITS, 'get', false);

export const getRecommendedUserArazCitiesAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUserArazCitiesAction',
  getRecommendedUserArazCities,
  'get',
  false
);

export const getRecommendedUserDepartmentsAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUserDepartmentsAction',
  getRecommendedUserDepartments,
  'get',
  false
);

export const getDepartmentWiseUsersAction = createGenericAsyncThunk(
  'hierarchy/getDepartmentWiseUsersAction',
  getDepartmentWiseUsers,
  'get',
  false
);

export const getTravelPriorityAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUserTravelPriority',
  getRecommendedUserTravelPriority,
  'get',
  false
);

export const getRazaRecommendationAction = createGenericAsyncThunk(
  'hierarchy/getRecommendedUserRazaRecommendation',
  getRecommendedUserRazaRecommendation,
  'get',
  false
);

export const addRecommendedUserAction = createGenericAsyncThunk(
  'hierarchy/addRecommendedUserAction',
  addHierarchyRecommendedUser,
  'get',
  false
);

export const addCompileListAction = createGenericAsyncThunk('hierarchy/addCompileListAction', addCompileList, 'get', false);

export const editRecommendedUserAction = createGenericAsyncThunk(
  'hierarchy/editRecommendedUserAction',
  editHierarchyRecommendedUser,
  'get',
  false
);

export const deleteRecommendedUserAction = createGenericAsyncThunk(
  'hierarchy/deleteRecommendedUserAction',
  deleteHierarchyRecommendedUser,
  'get',
  false
);
