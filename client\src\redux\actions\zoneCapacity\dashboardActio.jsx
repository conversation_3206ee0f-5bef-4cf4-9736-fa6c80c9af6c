import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityDashboardApiUrl, ZoneCapacityHotelsApiUrl } from 'utils/constant';

const getDashboardURL = (endponints) => {
  return `${ZoneCapacityDashboardApiUrl}${endponints}`;
};

const getZoneCapacityDashboard = (data, dispatch) => {
  return postRequest('zonesCapacity', getDashboardURL('get'), data, true, dispatch);
};

export const getZoneCapacityDashboardAction = createGenericAsyncThunk(
  'dashboard/getZoneCapacityDashboardAction',
  getZoneCapacityDashboard,
  'get'
);
