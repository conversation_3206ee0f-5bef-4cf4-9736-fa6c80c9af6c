import { createGenericAsyncThunk } from 'redux/helper';
import { postRequest } from 'utils/axios';
import { AsharaGuideDashboardApiUrl } from 'utils/constant';
const getDashboardURL = (endponints) => {
  return `${AsharaGuideDashboardApiUrl}${endponints}`;
};
const getDashboard = (data, dispatch) => {
  return postRequest('global', getDashboardURL('/get'), data, true, dispatch);
};

export const getAsharaGuideDashboardAction = createGenericAsyncThunk('dashboard/getAsharaGuideDashboardAction', getDashboard, 'get');
