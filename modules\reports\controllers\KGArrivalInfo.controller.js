const { apiResponse } = require("../../../utils/api.util");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");
const { FETCH, NOT_FOUND } = require("../../../utils/message.util");
const { KGArrivalInfo } = require("../../hierarchy/models/KGArrivalInfo.model");
const mongoose = require("mongoose");

const getKGArrivalInfo = async (req, res) => {
  const { arazCityID, miqaatID } = req.query;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  let arrivalInfos = await KGArrivalInfo.find({
    arazCityID: arazCityID,
    miqaatID: miqaatID,
  })
    .populate({ path: "miqaatID", select: "name" })
    .populate({ path: "arazCityID", select: "name" })
    .populate({
      path: "userID",
      select: "name ITSID phone appDetails",
      populate: {
        path: "miqaats",
        populate: [
          { path: "miqaatID", select: "name" },
          { path: "arazCityID", select: "name" },
          { path: "arazCityZoneID", select: "name" },
          { path: "hierarchyPositionID", select: "name alias" },
          { path: "kgTypeID", select: "name" },
          { path: "kgGroupID", select: "name" },
          { path: "departmentID", select: "name" },
          { path: "functionID", select: "name" },
        ],
      },
    });

  arrivalInfos = arrivalInfos.map((arrivalInfo) => {
    const user = arrivalInfo.userID;
    if (user && Array.isArray(user.miqaats)) {
      const matchingMiqaat = user.miqaats.find((miqaat) => {
        const miqaatIDMatch =
          miqaat.miqaatID && miqaat.miqaatID._id
            ? miqaat.miqaatID._id.toString() === miqaatID.toString()
            : miqaat.miqaatID.toString() === miqaatID.toString();

        const arazCityIDMatch =
          miqaat.arazCityID && miqaat.arazCityID._id
            ? miqaat.arazCityID._id.toString() === arazCityID.toString()
            : miqaat.arazCityID.toString() === arazCityID.toString();

        return miqaatIDMatch && arazCityIDMatch;
      });
      user.miqaats = matchingMiqaat || null;
    }
    return arrivalInfo;
  });

  if (!arrivalInfos || arrivalInfos.length === 0) {
    return apiResponse(NOT_FOUND, "Arrival Info", [], res);
  }

  // Generate data from June 6, 2025 to July 5, 2025 (30 days)
  const generateStaticDateRange = () => {
    const days = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];
    const startDate = new Date("2025-06-06"); // June 6, 2025
    const dateRange = [];

    for (let i = 0; i < 30; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);

      const dateString = date.toISOString().split("T")[0]; // Format: YYYY-MM-DD
      const dayName = days[date.getDay()];

      dateRange.push({
        date: dateString,
        day: dayName,
        users: 0,
      });
    }

    return dateRange;
  };

  // Count users by arrival date for the specified date range
  const arrivalChart = generateStaticDateRange();

 arrivalInfos.forEach((arrivalInfo) => {
  if (arrivalInfo.arrivalDate) {
    const arrivalDateString = new Date(arrivalInfo.arrivalDate)
      .toLocaleDateString("en-CA", { timeZone: "Asia/Kolkata" });

    const dayIndex = arrivalChart.findIndex(
      (day) => day.date === arrivalDateString
    );

    if (dayIndex !== -1) {
      arrivalChart[dayIndex].users += 1;
    }
  }
});


  // Count users by mode of travel
  // const modeOfTravelCount = {};
  // const modeColors = {
  //   Car: "#FF6384",
  //   Bus: "#FFCE56",
  //   Train: "#36A2EB",
  //   Flight: "#FF9F40",
  //   Other: "#C9CBCF",
  // };

  // arrivalInfos.forEach((arrivalInfo) => {
  //   if (arrivalInfo.modeOfTravel) {
  //     const mode = arrivalInfo.modeOfTravel;
  //     modeOfTravelCount[mode] = (modeOfTravelCount[mode] || 0) + 1;
  //   }
  // });

  // const modeOfTravelChart = Object.keys(modeOfTravelCount).map((mode) => ({
  //   name: mode,
  //   value: modeOfTravelCount[mode],
  //   color: modeColors[mode] || modeColors["Other"],
  // }));

  // Prepare response data
  const responseData = {
    arrivalInfos: arrivalInfos,
    arrivalChart: arrivalChart,
    // modeOfTravelChart: modeOfTravelChart,
    summary: {
      totalUsers: arrivalInfos.length,
      usersWithArrivalDate: arrivalInfos.filter((info) => info.arrivalDate)
        .length,
      usersArrivingInDateRange: arrivalChart.reduce(
        (sum, day) => sum + day.users,
        0
      ),
    },
  };

  return apiResponse(FETCH, "Arrival Info", responseData, res);
};

module.exports = {
  getKGArrivalInfo,
};
