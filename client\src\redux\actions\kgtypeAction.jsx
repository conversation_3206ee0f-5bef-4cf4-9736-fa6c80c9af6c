import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyKGTypeApiUrl } from 'utils/constant';

const getKGTypeURL = (endponints) => {
  return `${HierarchyKGTypeApiUrl}${endponints}`;
};

const getAllKGType = (data, dispatch) => {
  return getRequest('global', getKGTypeURL('get'), data, true, dispatch);
};

const getSingleKGType = (data, dispatch) => {
  return getRequest('global', getKGTypeURL(`get/${data}`), data, true, dispatch);
};

const addKGType = (data, dispatch) => {
  return postRequest('global', getKGTypeURL('add'), data, true, dispatch);
};

const updateKGType = (data, dispatch) => {
  return putRequest('global', getKGTypeURL('edit'), data, true, dispatch);
};

const deleteKGType = (data, dispatch) => {
  return deleletRequest('global', getKGTypeURL(`delete/${data}`), data, true, dispatch);
};

export const getAllKGTypeAction = createGenericAsyncThunk('KG/getAllKGTypeAction', getAllKGType, 'get');

export const getSingleKGTypeAction = createGenericAsyncThunk('KG/getSingleKGTypeAction', getSingleKGType, 'get');

export const addKGTypeAction = createGenericAsyncThunk('KG/addKGTypeAction', addKGType);

export const updateKGTypeAction = createGenericAsyncThunk('KG/updateKGTypeAction', updateKGType);

export const deleteKGTypeAction = createGenericAsyncThunk('KG/deleteKGTypeAction', deleteKGType);
