const express = require("express");
const { getDepartmentByArazCity } = require("../modules/globalMasters/controllers/department.controller");
const { getAllFunctions, getSingleFunction, getFunctionsByDepartment } = require("../modules/globalMasters/controllers/function.controller");

const { validate, validateV2 } = require("../middlewares/validation.middleware");
const { getSingleMiqaatSchema } = require("../modules/globalMasters/validations/miqaat.validation");
const { getSingleArazCitySchema, getArazCityByMiqaatSchema } = require("../modules/globalMasters/validations/arazCity.validation");
const { getDepartmentByArazCitySchema, getSingleDepartmentSchema } = require("../modules/globalMasters/validations/department.validation");
const { getSingleFunctionSchema, getFunctionsByDepartmentSchema } = require("../modules/globalMasters/validations/function.validation");
const {  getSingleArazCityZone } = require("../modules/hierarchy/controllers/arazCityZone.controller");
const { getSingleHierarchyPositionSchema } = require("../modules/globalMasters/validations/hierarchyPosition.validation");
const { getHierarchySchema } = require("../modules/hierarchy/validations/hierarchy.validation");
const { getHierarchy } = require("../modules/hierarchy/controllers/hierarchy.controller");
const { getAllMiqaats,getSingleMiqaat, getArazCityByMiqaat,getSingleArazCity,getArazCityZoneByArazCity,getAllDepartments,getSingleDepartment,getAllHierarchyPositions,getSingleHierarchyPosition, getKGUsers, getRazaStatus} = require("./controllers/public.controller");
const { getKgListSchema, getRazaStatusSchema } = require("./validations/public.validation");

const router = express.Router();

// Miqaat routes
router.get("/miqaat", getAllMiqaats); // Get all miqaats
router.get("/miqaat/:id", validate(getSingleMiqaatSchema, "params"), getSingleMiqaat); // Get a specific miqaat by id

// Araz city routes
router.get("/araz-cities/:id", validate(getArazCityByMiqaatSchema, "params"), getArazCityByMiqaat); // Get araz cities by miqaat id
router.get("/araz-city/:id", validate(getSingleArazCitySchema, "params"), getSingleArazCity); // Get a specific araz city by id

// Araz city zone routes
router.get("/araz-city-zones/:id", validate(getSingleArazCitySchema, "params"), getArazCityZoneByArazCity); // Get zones by araz city id
router.get("/araz-city-zone/:id", validate(getArazCityByMiqaatSchema, "params"), getSingleArazCityZone); // Get a specific zone by id

// Department routes
router.get("/department", getAllDepartments); // Get all departments
router.get("/araz-city-department/:id", validate(getDepartmentByArazCitySchema, "params"), getDepartmentByArazCity); // Get departments by araz city id
router.get("/department/:id", validate(getSingleDepartmentSchema, "params"), getSingleDepartment); // Get a specific department by id

// Hierarchy position routes
router.get("/hierarchy-position", getAllHierarchyPositions); // Get all hierarchy positions
router.get("/hierarchy-position/:id", validate(getSingleHierarchyPositionSchema, "params"), getSingleHierarchyPosition); // Get a specific hierarchy position by id

// Function routes
router.get("/function", getAllFunctions); // Get all functions
router.get("/function/:id", validate(getSingleFunctionSchema, "params"), getSingleFunction); // Get a specific function by id
router.get("/function/by-department/:id", validate(getFunctionsByDepartmentSchema, "params"), getFunctionsByDepartment); // Get functions by department id

// Hierarchy route
router.post("/hierarchy", validateV2(getHierarchySchema, "body"), getHierarchy); // Get hierarchy based on request body i.e. tree or departmental

//KG List route
router.post('/kg-list',validate(getKgListSchema,'query'),getKGUsers)

//Get Raza Status
router.get('/raza-status',validate(getRazaStatusSchema,'query'),getRazaStatus)

module.exports = router;