const { validate } = require("../../../middlewares/validation.middleware");
const { upload } = require("../../../middlewares/multer.middleware");

const {
  parseJsonFields,
  parseDateFields,
} = require("../../../middlewares/fieldProcessors.middleware");

const {
  getAllArazCities,
  addArazCity,
  getSingleArazCity,
  editArazCity,
  deleteArazCity,
  getArazCityByMiqaat,
  getTimezones,
} = require("../controllers/arazCity.controller");

const {
  addArazCitySchema,
  getSingleArazCitySchema,
  editArazCitySchema,
  deleteArazCitySchema,
  getArazCityByMiqaatSchema,
} = require("../validations/arazCity.validation");

const router = require("express").Router();

router.get("/get", getAllArazCities);

router.get("/get/timezone", getTimezones);

router.post(
  "/add",
  upload("arazCities").single("logo"),
  parseJsonFields(["departments"]),
  validate(addArazCitySchema, "body"),
  addArazCity
);

router.get(
  "/get/:id",
  validate(getSingleArazCitySchema, "params"),
  getSingleArazCity
);

router.put(
  "/edit",
  upload("arazCities").single("logo"),
  parseJsonFields(["departments"]),
  validate(editArazCitySchema, "body"),
  editArazCity
);

router.delete(
  "/delete/:id",
  validate(deleteArazCitySchema, "params"),
  deleteArazCity
);

router.get(
  "/get/by-miqaat/:id",
  validate(getArazCityByMiqaatSchema, "params"),
  getArazCityByMiqaat
);


module.exports = router;
