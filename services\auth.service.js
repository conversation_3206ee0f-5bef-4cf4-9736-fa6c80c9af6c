const express = require("express");
const cors = require("cors");
const path = require("path");
const { CORS_ORIGIN, NODE_ENV, AUTH_SERVICE_PORT } = require("../constants");
const routes = require("../modules/user/routes");
const { authGuard, roleGuard } = require("../middlewares/guard.middleware");
const { connectDB } = require("../db");

const app = express();

app.use(cors({ credentials: true, origin: CORS_ORIGIN }));
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "1mb" }));
app.use((err, req, res, next) => {
  if (err instanceof SyntaxError) {
    return res
      .status(400)
      .json({ success: false, message: "Invalid JSON format" });
  }
  next(err);
});

app.use("/api", authGuard, roleGuard, routes);

app.get("/", (req, res) => {
  res.send(`Authentication Service is running on port: ${AUTH_SERVICE_PORT}`);
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "healthy",
    service: "authentication",
    port: AUTH_SERVICE_PORT,
    timestamp: new Date().toISOString(),
  });
});

// Start server
const startServer = async () => {
  try {
    await connectDB();
    app.listen(AUTH_SERVICE_PORT, () => {
      console.log(`Node environment: ${NODE_ENV}`);
      console.log(
        `Authentication Service running on port: ${AUTH_SERVICE_PORT}`
      );
    });
  } catch (err) {
    console.log(`DB Error: ${err.message}`);
    process.exit(1);
  }
};

if (require.main === module) {
  startServer();
}

module.exports = app;
