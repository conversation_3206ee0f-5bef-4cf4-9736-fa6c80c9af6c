const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  CUSTOM_SUCCESS,
  FETCH,
  CUSTOM_ERROR,
  NOT_FOUND,
  ADD_SUCCESS
} = require("../../../utils/message.util");
const { toObjectId, sanitizeHtml } = require("../../../utils/misc.util");
const { notificationTemplate, sendPushNotifications } = require("../../../utils/oneSignal.util");
const { webhookQueue } = require("../../../utils/queues");
const { NotificationLog, messageTypes, Meeting } = require("../../communication/models");
const { KGUser } = require("../../hierarchy/models");

const getNotifications = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
  const { page, limit } = req.query;

  const aggregate = NotificationLog.aggregate([
    {
      $match: {
        messageType: messageTypes.NOTIFICATION,
        "recipients.userID": toObjectId(loggedInUserID),
      },
    },
    {
      $project: {
        title: {
          $cond: [
            { $gt: [{ $strLenCP: "$title" }, 50] },
            { $concat: [{ $substrCP: ["$title", 0, 50] }, "..."] },
            "$title",
          ],
        },
        message: {
          $cond: [
            { $gt: [{ $strLenCP: "$message" }, 50] },
            { $concat: [{ $substrCP: ["$message", 0, 50] }, "..."] },
            "$message",
          ],
        },
        createdAt: 1,
        updatedAt: 1,
      },
    },
    { $sort: { createdAt: -1 } },
  ]);

  const options = {
    page: page || 1,
    limit: limit || 10,
  };

  const result = await NotificationLog.aggregatePaginate(aggregate, options);

  return apiResponse(FETCH, "Notifications", result, res);
});

const getNotification = apiHandler(async (req, res) => {
  const { _id: loggedInUserID } = req.user;
  const { id: notificationID } = req.params;

  const notificationLog = await NotificationLog.findOne({
    _id: toObjectId(notificationID),
    "recipients.userID": toObjectId(loggedInUserID),
    messageType: messageTypes.NOTIFICATION,
  }).select("title message createdAt updatedAt");
  if (!notificationLog) {
    return apiError(NOT_FOUND, "Notification", null, res);
  }

  return apiResponse(FETCH, "Notifications", notificationLog, res);
});

const addNotification = apiHandler(async (req, res) => {
  const payload = req.body;
  // const payload = {
  //   action: "work_package:updated",
  //   work_package: {
  //     _type: "WorkPackage",
  //     id: 14792,
  //     lockVersion: 11,
  //     subject: "1. Planning Phase",
  //     description: { format: "markdown", raw: "", html: "" },
  //     scheduleManually: false,
  //     startDate: null,
  //     dueDate: null,
  //     derivedStartDate: null,
  //     derivedDueDate: null,
  //     estimatedTime: null,
  //     derivedEstimatedTime: null,
  //     derivedRemainingTime: null,
  //     duration: null,
  //     ignoreNonWorkingDays: false,
  //     spentTime: "PT0S",
  //     percentageDone: null,
  //     derivedPercentageDone: null,
  //     createdAt: "2025-04-13T07:20:22.440Z",
  //     updatedAt: "2025-05-07T12:14:44.419Z",
  //     laborCosts: "0.00 EUR",
  //     materialCosts: "0.00 EUR",
  //     overallCosts: "0.00 EUR",
  //     _embedded: {
  //       attachments: {
  //         _type: "Collection",
  //         total: 0,
  //         count: 0,
  //         _embedded: { elements: [] },
  //         _links: {
  //           self: { href: "/api/v3/work_packages/14792/attachments" },
  //         },
  //       },
  //       relations: {
  //         _type: "Collection",
  //         total: 0,
  //         count: 0,
  //         _embedded: { elements: [] },
  //         _links: {
  //           self: { href: "/api/v3/work_packages/14792/relations" },
  //         },
  //       },
  //       type: {
  //         _type: "Type",
  //         id: 3,
  //         name: "Phase",
  //         color: "#FF922B",
  //         position: 3,
  //         isDefault: true,
  //         isMilestone: false,
  //         createdAt: "2025-04-05T06:46:04.709Z",
  //         updatedAt: "2025-04-05T06:46:04.709Z",
  //         _links: { self: { href: "/api/v3/types/3", title: "Phase" } },
  //       },
  //       priority: {
  //         _type: "Priority",
  //         id: 8,
  //         name: "Normal",
  //         position: 2,
  //         color: "#74C0FC",
  //         isDefault: true,
  //         isActive: true,
  //         _links: {
  //           self: { href: "/api/v3/priorities/8", title: "Normal" },
  //         },
  //       },
  //       project: {
  //         _type: "Project",
  //         id: 1426,
  //         identifier: "ammar-nagar---hr-hr-am-1447-ohbat-indore",
  //         name: "Ammar Nagar - Hr",
  //         active: true,
  //         public: false,
  //         description: { format: "markdown", raw: "", html: "" },
  //         createdAt: "2025-04-10T09:59:08.119Z",
  //         updatedAt: "2025-04-24T09:17:02.130Z",
  //         statusExplanation: { format: "markdown", raw: "", html: "" },
  //         _links: {
  //           self: {
  //             href: "/api/v3/projects/1426",
  //             title: "Ammar Nagar - Hr",
  //           },
  //           createWorkPackage: {
  //             href: "/api/v3/projects/1426/work_packages/form",
  //             method: "post",
  //           },
  //           createWorkPackageImmediately: {
  //             href: "/api/v3/projects/1426/work_packages",
  //             method: "post",
  //           },
  //           workPackages: { href: "/api/v3/projects/1426/work_packages" },
  //           storages: [],
  //           categories: { href: "/api/v3/projects/1426/categories" },
  //           versions: { href: "/api/v3/projects/1426/versions" },
  //           memberships: {
  //             href: "/api/v3/memberships?filters=%5B%7B%22project%22%3A%7B%22operator%22%3A%22%3D%22%2C%22values%22%3A%5B%221426%22%5D%7D%7D%5D",
  //           },
  //           types: { href: "/api/v3/projects/1426/types" },
  //           update: { href: "/api/v3/projects/1426/form", method: "post" },
  //           updateImmediately: {
  //             href: "/api/v3/projects/1426",
  //             method: "patch",
  //           },
  //           delete: { href: "/api/v3/projects/1426", method: "delete" },
  //           schema: { href: "/api/v3/projects/schema" },
  //           ancestors: [
  //             { href: "/api/v3/projects/833", title: "Indore" },
  //             { href: "/api/v3/projects/834", title: "AM 1447 Ohbat" },
  //             { href: "/api/v3/projects/837", title: "Hr" },
  //           ],
  //           projectStorages: {
  //             href: "/api/v3/project_storages?filters=%5B%7B%22projectId%22%3A%7B%22operator%22%3A%22%3D%22%2C%22values%22%3A%5B%221426%22%5D%7D%7D%5D",
  //           },
  //           parent: { href: "/api/v3/projects/837", title: "Hr" },
  //           status: { href: null },
  //         },
  //       },
  //       status: {
  //         _type: "Status",
  //         id: 1,
  //         name: "Open",
  //         isClosed: false,
  //         color: "#1098AD",
  //         isDefault: true,
  //         isReadonly: false,
  //         excludedFromTotals: false,
  //         defaultDoneRatio: 0,
  //         position: 1,
  //         _links: { self: { href: "/api/v3/statuses/1", title: "Open" } },
  //       },
  //       author: {
  //         _type: "User",
  //         id: 4,
  //         name: "OpenProject Admin",
  //         createdAt: "2025-04-05T06:46:05.192Z",
  //         updatedAt: "2025-05-07T12:16:22.803Z",
  //         login: "admin",
  //         admin: true,
  //         firstName: "OpenProject",
  //         lastName: "Admin",
  //         email: "<EMAIL>",
  //         avatar:
  //           "https://secure.gravatar.com/avatar/e5ba9d95ed528c4c7e32646af1ac8325?default=404\u0026secure=true",
  //         status: "active",
  //         identityUrl: null,
  //         language: "en",
  //         _links: {
  //           self: { href: "/api/v3/users/4", title: "OpenProject Admin" },
  //           memberships: {
  //             href: "/api/v3/memberships?filters=%5B%7B%22principal%22%3A%7B%22operator%22%3A%22%3D%22%2C%22values%22%3A%5B%224%22%5D%7D%7D%5D",
  //             title: "Memberships",
  //           },
  //           showUser: { href: "/users/4", type: "text/html" },
  //           updateImmediately: {
  //             href: "/api/v3/users/4",
  //             title: "Update admin",
  //             method: "patch",
  //           },
  //           lock: {
  //             href: "/api/v3/users/4/lock",
  //             title: "Set lock on admin",
  //             method: "post",
  //           },
  //           delete: {
  //             href: "/api/v3/users/4",
  //             title: "Delete admin",
  //             method: "delete",
  //           },
  //         },
  //       },
  //       customActions: [],
  //       costsByType: {
  //         _type: "Collection",
  //         total: 0,
  //         count: 0,
  //         _embedded: { elements: [] },
  //         _links: {
  //           self: {
  //             href: "/api/v3/work_packages/14792/summarized_costs_by_type",
  //           },
  //         },
  //       },
  //     },
  //     _links: {
  //       attachments: { href: "/api/v3/work_packages/14792/attachments" },
  //       addAttachment: {
  //         href: "/api/v3/work_packages/14792/attachments",
  //         method: "post",
  //       },
  //       fileLinks: { href: "/api/v3/work_packages/14792/file_links" },
  //       addFileLink: {
  //         href: "/api/v3/work_packages/14792/file_links",
  //         method: "post",
  //       },
  //       self: {
  //         href: "/api/v3/work_packages/14792",
  //         title: "1. Planning Phase",
  //       },
  //       update: {
  //         href: "/api/v3/work_packages/14792/form",
  //         method: "post",
  //       },
  //       schema: { href: "/api/v3/work_packages/schemas/1426-3" },
  //       updateImmediately: {
  //         href: "/api/v3/work_packages/14792",
  //         method: "patch",
  //       },
  //       delete: { href: "/api/v3/work_packages/14792", method: "delete" },
  //       logTime: {
  //         href: "/api/v3/time_entries",
  //         title: "Log time on work package '1. Planning Phase'",
  //       },
  //       move: {
  //         href: "/work_packages/14792/move/new",
  //         type: "text/html",
  //         title: "Move work package '1. Planning Phase'",
  //       },
  //       copy: {
  //         href: "/work_packages/14792/copy",
  //         type: "text/html",
  //         title: "Copy work package '1. Planning Phase'",
  //       },
  //       pdf: {
  //         href: "/work_packages/14792.pdf",
  //         type: "application/pdf",
  //         title: "Export as PDF",
  //       },
  //       generate_pdf: {
  //         href: "/work_packages/14792/generate_pdf_dialog",
  //         type: "text/vnd.turbo-stream.html",
  //         title: "Generate PDF",
  //       },
  //       atom: {
  //         href: "/work_packages/14792.atom",
  //         type: "application/rss+xml",
  //         title: "Atom feed",
  //       },
  //       availableRelationCandidates: {
  //         href: "/api/v3/work_packages/14792/available_relation_candidates",
  //         title: "Potential work packages to relate to",
  //       },
  //       customFields: {
  //         href: "/projects/ammar-nagar---hr-hr-am-1447-ohbat-indore/settings/custom_fields",
  //         type: "text/html",
  //         title: "Custom fields",
  //       },
  //       configureForm: {
  //         href: "/types/3/edit?tab=form_configuration",
  //         type: "text/html",
  //         title: "Configure form",
  //       },
  //       activities: { href: "/api/v3/work_packages/14792/activities" },
  //       availableWatchers: {
  //         href: "/api/v3/work_packages/14792/available_watchers",
  //       },
  //       relations: { href: "/api/v3/work_packages/14792/relations" },
  //       revisions: { href: "/api/v3/work_packages/14792/revisions" },
  //       watchers: { href: "/api/v3/work_packages/14792/watchers" },
  //       addWatcher: {
  //         href: "/api/v3/work_packages/14792/watchers",
  //         method: "post",
  //         payload: { user: { href: "/api/v3/users/{user_id}" } },
  //         templated: true,
  //       },
  //       removeWatcher: {
  //         href: "/api/v3/work_packages/14792/watchers/{user_id}",
  //         method: "delete",
  //         templated: true,
  //       },
  //       addRelation: {
  //         href: "/api/v3/work_packages/14792/relations",
  //         method: "post",
  //         title: "Add relation",
  //       },
  //       addChild: {
  //         href: "/api/v3/projects/ammar-nagar---hr-hr-am-1447-ohbat-indore/work_packages",
  //         method: "post",
  //         title: "Add child of 1. Planning Phase",
  //       },
  //       changeParent: {
  //         href: "/api/v3/work_packages/14792",
  //         method: "patch",
  //         title: "Change parent of 1. Planning Phase",
  //       },
  //       addComment: {
  //         href: "/api/v3/work_packages/14792/activities",
  //         method: "post",
  //         title: "Add comment",
  //       },
  //       previewMarkup: {
  //         href: "/api/v3/render/markdown?context=/api/v3/work_packages/14792",
  //         method: "post",
  //       },
  //       timeEntries: {
  //         href: "/api/v3/time_entries?filters=%5B%7B%22work_package_id%22%3A%7B%22operator%22%3A%22%3D%22%2C%22values%22%3A%5B%2214792%22%5D%7D%7D%5D",
  //         title: "Time entries",
  //       },
  //       children: [
  //         {
  //           href: "/api/v3/work_packages/14867",
  //           title: "1.6 Budget Preparation",
  //         },
  //         {
  //           href: "/api/v3/work_packages/14793",
  //           title: "1.1 Team Formation and Role Assignment",
  //         },
  //         {
  //           href: "/api/v3/work_packages/14805",
  //           title: "1.2 Data Collection and Requirements Gathering",
  //         },
  //         {
  //           href: "/api/v3/work_packages/14852",
  //           title: "1.5 Logistics and Setup",
  //         },
  //         {
  //           href: "/api/v3/work_packages/14830",
  //           title: "1.3 Resource Planning and Recruitment",
  //         },
  //         {
  //           href: "/api/v3/work_packages/14843",
  //           title: "1.4 Orientation and Training",
  //         },
  //       ],
  //       ancestors: [],
  //       category: { href: null },
  //       type: { href: "/api/v3/types/3", title: "Phase" },
  //       priority: { href: "/api/v3/priorities/8", title: "Normal" },
  //       project: {
  //         href: "/api/v3/projects/1426",
  //         title: "Ammar Nagar - Hr",
  //       },
  //       status: { href: "/api/v3/statuses/1", title: "Open" },
  //       author: { href: "/api/v3/users/4", title: "OpenProject Admin" },
  //       responsible: { href: null },
  //       assignee: { href: null },
  //       version: { href: null },
  //       parent: { href: null, title: null },
  //       customActions: [],
  //       logCosts: {
  //         href: "/work_packages/14792/cost_entries/new",
  //         type: "text/html",
  //         title: "Log costs on 1. Planning Phase",
  //       },
  //       showCosts: {
  //         href: "/projects/1426/cost_reports?fields%5B%5D=WorkPackageId\u0026operators%5BWorkPackageId%5D=%3D\u0026set_filter=1\u0026values%5BWorkPackageId%5D=14792",
  //         type: "text/html",
  //         title: "Show cost entries",
  //       },
  //       costsByType: {
  //         href: "/api/v3/work_packages/14792/summarized_costs_by_type",
  //       },
  //       github: {
  //         href: "/work_packages/14792/tabs/github",
  //         title: "github",
  //       },
  //       github_pull_requests: {
  //         href: "/api/v3/work_packages/14792/github_pull_requests",
  //         title: "GitHub pull requests",
  //       },
  //       gitlab: {
  //         href: "/work_packages/14792/tabs/gitlab",
  //         title: "gitlab",
  //       },
  //       gitlab_merge_requests: {
  //         href: "/api/v3/work_packages/14792/gitlab_merge_requests",
  //         title: "Gitlab merge requests",
  //       },
  //       gitlab_issues: {
  //         href: "/api/v3/work_packages/14792/gitlab_issues",
  //         title: "Gitlab Issues",
  //       },
  //       meetings: {
  //         href: "/work_packages/14792/tabs/meetings",
  //         title: "meetings",
  //       },
  //       convertBCF: {
  //         href: "/api/bcf/2.1/projects/ammar-nagar---hr-hr-am-1447-ohbat-indore/topics",
  //         title: "Convert to BCF",
  //         payload: { reference_links: ["/api/v3/work_packages/14792"] },
  //         method: "post",
  //       },
  //     },
  //   },
  // };

  if (!payload) {
    console.log("no/invalid payload provided");
    return;
  }

  await webhookQueue.addWebhook(payload);
  // const allJobs = await webhookQueue.getJobs();
  // console.log(allJobs);

  return apiResponse(
    CUSTOM_SUCCESS,
    "Notification sent successfully from open project",
    null,
    res
  );
});

const sendMeetingNotification = apiHandler(async (req, res) => {
  const { type, meetingID } = req.body;

  const meeting = await Meeting.findById(meetingID).select("title meetingUrl meetingAddress date time member mom").lean();
  if (!meeting) {
    return apiError(NOT_FOUND, "Meeting", null, res);
  }

  if (type === "SEND_REMINDER") {
    await commanSendReminderAndNotofication(meeting, "SEND_REMINDER")
    return apiResponse(CUSTOM_SUCCESS, "Reminder sent successfully", null, res);
  } else if (type === "SEND_MOM_REMINDER") {
    await commanSendReminderAndNotofication(meeting, "SEND_MOM_REMINDER");
    return apiResponse(CUSTOM_SUCCESS, "MOM notification sent successfully", null, res);
  } else {
    return apiError(CUSTOM_ERROR, "Invalid notification type", null, res);
  }
});

const commanSendReminderAndNotofication = async (meeting, type) => {
  // Format date and time
  const formattedDate = meeting.date.toISOString().split('T')[0]; // "2025-05-24"
  const time = meeting.time[0];
  const formattedTime = `${time.hh}:${String(time.mm).padStart(2, '0')} ${time.period}`; // "4:30 AM"
  // console.log("Date:", formattedDate);
  // console.log("Time:", formattedTime);

  const users = await KGUser.find({ _id: { $in: meeting.member } }).select('_id');

  // Construct optional parts
  let messageParts = [];
  if (meeting.meetingUrl) messageParts.push(`Online: <a href="${meeting.meetingUrl}">${meeting.meetingUrl}</a>`);
  if (meeting.meetingAddress) messageParts.push(`Address: ${meeting.meetingAddress}`);
  messageParts.push(`Date and Time: ${formattedDate}, ${formattedTime}`);

  if (type === "SEND_REMINDER") {
    const notificationLogData = {
      messageID: null,
      replyID: null,
      messageType: messageTypes.NOTIFICATION,
      recipients: users.map(r => r._id.toString())
    }

    // Construct the notification data
    const notificationTemplateData = {
      title: `Reminder: ${meeting.title}`,
      message: sanitizeHtml(messageParts.join('<br>'), {
        allowedTags: ['br', 'a'],
        allowedAttributes: {
          a: ['href']
        }
      })
    };

    await sendPushNotifications(notificationLogData, notificationTemplate.NOTIFICATION(notificationTemplateData))
    // return apiResponse(CUSTOM_SUCCESS, "Reminder sent successfully", null, res);

  } else if (type === "SEND_MOM_REMINDER") {

    const notificationLogData = {
      messageID: null,
      replyID: null,
      messageType: messageTypes.NOTIFICATION,
      recipients: users.map(r => r._id.toString())
    }

    const notificationTemplateData = {
      title: `Minutes of Meeting: ${meeting.title}`,
      message: `${meeting.mom}`
    }

    await sendPushNotifications(notificationLogData, notificationTemplate.NOTIFICATION(notificationTemplateData))
    // return apiResponse(CUSTOM_SUCCESS, "MOM notification sent successfully", null, res);
  }
};




module.exports = {
  getNotifications,
  getNotification,
  addNotification,
  sendMeetingNotification,
  commanSendReminderAndNotofication
};
