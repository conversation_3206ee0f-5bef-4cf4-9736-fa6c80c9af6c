const Joi = require("joi");
const { idValidation, arrayIdValidation, arrayStringValidation } = require("../../../utils/validator.util");

const addSmeMappingUserSchema = Joi.object({
  departmentID: idValidation,
  ITSIDs: arrayStringValidation
})

const deleteSmeUserSchema = Joi.object({
  departmentID: idValidation,
  kgUserID: idValidation,
})

module.exports = {
  addSmeMappingUserSchema,
  deleteSmeUserSchema
}