const Joi = require("joi");
const {
  idValidation,
  stringValidation,
  arrayIdValidation,
} = require("../../../utils/validator.util");

const dateStringValidation = Joi.string().pattern(/^\d{2}-\d{2}-\d{4}$/).required();

const markAttendanceSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  latitude: stringValidation,
  longitude: stringValidation,
});

const getProfileSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
});

const getDropdownDataSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  type: stringValidation.valid("DEPARTMENT", "ZONE"),
  arazCityZoneID: idValidation.optional(),
});

const getAttendanceReportSchema = Joi.object({
  miqaatID: idValidation,
  arazCityID: idValidation,
  startDate: dateStringValidation,
  endDate: dateStringValidation,
  arazCityZoneID: arrayIdValidation.optional(),
  departmentID: arrayIdValidation.optional(),
});

module.exports = {
  markAttendanceSchema,
  getProfileSchema,
  getDropdownDataSchema,
  getAttendanceReportSchema,
};
