const { Schema, model } = require("mongoose");

const clickSchema = new Schema(
  {
    userID: {
      type: Schema.Types.ObjectId,
      ref: "KGUser",
      required: false,
    },
    clickedAt: {
      type: Date,
      required: false,
    },
  },
  { _id: false }
);

const attachmentSchema = new Schema(
  {
    fileName: {
      type: String,
      required: false,
    },
    fileType: {
      type: String,
      required: false,
    },
    fileSize: {
      type: Number,
      required: false,
    },
    fileKey: {
      type: String,
      required: false,
    },
    uploadDate: {
      type: Date,
      required: false,
    },
    clicks: [clickSchema],
  },
  { timestamps: true, required: false }
);

const guideSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    awsGroupID: {
      type: String,
      required: false,
    },
    departments: [
      {
        type: Schema.Types.ObjectId,
        ref: "Department",
        required: false,
      },
    ],
    attachments: [attachmentSchema],
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
    updatedBy: {
      type: Schema.Types.ObjectId,
      ref: "User",
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const Guide = model("Guide", guideSchema);

module.exports = { Guide };
