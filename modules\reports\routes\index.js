const express = require("express");
const router = express.Router();

const arazCityHrDashboardRoute = require("./arazCityHrDashboard.route");
const whiteListDashboard1Route = require("./whiteListDashboard1.route");
const whiteListDashboard2Route = require("./whiteListDashboard2.route");
const arazCityDashboardRoute = require("./arazCityDashboard.route")
const arazCityZoneWiseRoute = require("./arazCityZoneWise.route")
const arrivalReportRoute = require("./arrivalReport.route");
const zoneWiseReportRoute = require('./zoneWise.route');

router.use("/araz-city-hr-dashboard", arazCityHrDashboardRoute);
router.use("/white-list-dashboard-1", whiteListDashboard1Route);
router.use("/white-list-dashboard-2", whiteListDashboard2Route);
router.use("/araz-city-dashboard", arazCityDashboardRoute);
router.use("/araz-city-zone-wise", arazCityZoneWiseRoute);
router.use("/arrival-report", arrivalReportRoute);
router.use("/zone-wise", zoneWiseReportRoute)


module.exports = router;
