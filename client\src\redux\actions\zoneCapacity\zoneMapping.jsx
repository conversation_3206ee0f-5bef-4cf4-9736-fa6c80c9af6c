import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityZoneMappingApiUrl } from 'utils/constant';

const getZoneMappingUrlURL = (endponints) => {
  return `${ZoneCapacityZoneMappingApiUrl}${endponints}`;
};

const getAllZoneMappings = (data, dispatch) => {
  return postRequest('zonesCapacity', getZoneMappingUrlURL('get'), data, true, dispatch);
};

const getSingleZoneMapping = (data, dispatch) => {
  return getRequest('zonesCapacity', getZoneMappingUrlURL(`get/${data}`), data, true, dispatch);
};

const DeleteZoneMapping = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getZoneMappingUrlURL(`delete/${data}`), data, true, dispatch);
};

const addZoneMappingByITS = (data, dispatch) => {
  return postRequest('zonesCapacity', 'zones-capacity/zone-mapping/add/by-hof', data, true, dispatch);
};

const addZoneMappingByJamiatJamaat = (data, dispatch) => {
  return postRequest('zonesCapacity', 'zones-capacity/zone-mapping/add/by-jamaat', data, true, dispatch);
};

const getJamaatJamiatByArazCity = (data, dispatch) => {
  return postRequest('zonesCapacity', 'zones-capacity/zone-mapping/get/jammat-jamiat-by-arazcity', data, true, dispatch);
};

export const getAllZoneMappingsAction = createGenericAsyncThunk('zoneMapping/getAllZoneMappingsAction', getAllZoneMappings, 'get');

export const DeleteZoneMappingAction = createGenericAsyncThunk('zoneMapping/DeleteZoneMappingAction', DeleteZoneMapping, 'get');

export const getSingleZoneMappingAction = createGenericAsyncThunk('zoneMapping/getSingleZoneMappingAction', getSingleZoneMapping, 'get');

export const addZoneMappingByITSAction = createGenericAsyncThunk('zoneMapping/addZoneMappingByITS', addZoneMappingByITS);

export const addZoneMappingByJamiatJamaatAction = createGenericAsyncThunk(
  'zoneMapping/addZoneMappingByJamiatJamaat',
  addZoneMappingByJamiatJamaat
);

export const getJamaatJamiatByArazCityAction = createGenericAsyncThunk('zoneMapping/getJamaatJamiatByArazCity', getJamaatJamiatByArazCity);
