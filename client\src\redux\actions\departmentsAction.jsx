import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyActiveDeactiveDepApiUrl, HierarchyDepartmentApiUrl, HierarchyDepReportApiUrl } from 'utils/constant';
import { getArazCityURL } from './arazCityAction';
import { get } from 'lodash';

const getDepartmentURL = (endponints) => {
  return `${HierarchyDepartmentApiUrl}${endponints}`;
};
const getDepartmentReportURL = (endponints) => {
  return `${HierarchyDepReportApiUrl}${endponints}`;
};

const getDepartments = (data, dispatch) => {
  return getRequest('global', getDepartmentURL('get'), data, true, dispatch);
};

const getActiveDepartmentByArazCity = (data, dispatch) => {
  const arazCityID = typeof data === 'object' ? data?.arazCityID : data;
  let url = `get/by-araz-city/${arazCityID}?isActive=true`;

  if (typeof data === 'object' && data?.showForKhidmatInterest) {
    url += `&showForKhidmatInterest=${data.showForKhidmatInterest}`;
  }

  return getRequest('global', getDepartmentURL(url), data, true, dispatch);
};

const getDepartmentByArazCity = (data, dispatch) => {
  return getRequest('global', getDepartmentURL(`get/by-araz-city/${data}`), data, true, dispatch);
};

const getSingleDepartment = (data, dispatch) => {
  return getRequest('global', getDepartmentURL(`get/${data}`), data, true, dispatch);
};

const addDepartment = (data, dispatch) => {
  return postRequest('global', getDepartmentURL('add'), data, true, dispatch);
};

const activateDeacitvate = (data, dispatch) => {
  return patchRequest('hierarchy', `${HierarchyActiveDeactiveDepApiUrl}edit/update-status`, data, true, dispatch);
};

const updateDepartmentKhidmatStatus = (data, dispatch) => {
  return patchRequest('hierarchy', `${HierarchyActiveDeactiveDepApiUrl}edit/update-khidmat-status`, data, true, dispatch);
};

const getDepartmentReport = (data, dispatch) => {
  const { kgTypes, ...rest } = data;
  const payload = kgTypes && kgTypes?.length > 0 ? data : rest;

  return postRequest('hierarchy', getDepartmentReportURL('get'), payload, true, dispatch);
};

const getDepartmentHODReport = (data, dispatch) => {
  const { kgTypes, ...rest } = data;
  const payload = kgTypes && kgTypes?.length > 0 ? data : rest;
  return postRequest('hierarchy', `${getDepartmentReportURL('get')}/department-wise-hod-report`, payload, true, dispatch);
};

const updateDepartment = (data, dispatch) => {
  return putRequest('global', getDepartmentURL('edit'), data, true, dispatch);
};

const deleteDepartment = (data, dispatch) => {
  return deleletRequest('global', getDepartmentURL(`delete/${data}`), data, true, dispatch);
};

export const getDepartmentsAction = createGenericAsyncThunk('departments/getDepartmentsAction', getDepartments, 'get');

export const getSingleDepartmentAction = createGenericAsyncThunk('departments/getSingleDepartmentAction', getSingleDepartment, 'get');

export const addDepartmentAction = createGenericAsyncThunk('departments/addDepartmentsAction', addDepartment);

export const updateDepartmentAction = createGenericAsyncThunk('departments/updateDepartmentAction', updateDepartment);

export const deleteDepartmentAction = createGenericAsyncThunk('departments/deleteDepartmentAction', deleteDepartment);

export const getActiveDepartmentByArazCityAction = createGenericAsyncThunk(
  'departments/getActiveDepartmentByArazCityAction',
  getActiveDepartmentByArazCity,
  'get'
);

export const getDepartmentByArazCityAction = createGenericAsyncThunk(
  'departments/getDepartmentByArazCityAction',
  getDepartmentByArazCity,
  'get'
);

export const activateDeacitvateAction = createGenericAsyncThunk('departments/activateDeacitvateAction', activateDeacitvate);

export const getDepartmentReportAction = createGenericAsyncThunk('departments/getDepartmentReportAction', getDepartmentReport, 'get');
export const getDepartmentHODReportAction = createGenericAsyncThunk('departments/getDepartmentHODReport', getDepartmentHODReport, 'get');
export const updateDepartmentKhidmatStatusAction = createGenericAsyncThunk(
  'departments/updateDepartmentKhidmatStatusAction',
  updateDepartmentKhidmatStatus
);
