const {
  api<PERSON><PERSON><PERSON>,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const { FETCH, CUSTOM_ERROR } = require("../../../utils/message.util");
const { isEmpty, toObjectId } = require("../../../utils/misc.util");
const Hotel = require("../../muqimeenMehmaanMapping/models/hotel.model");
const { WaazVenue, MawaidVenue } = require("../../zonesCapacity/models");
const {
  KGUser,
  HierarchyPosition,
  ArazCity,
  KGType,
  SystemRole,
  Miqaat,
  ArazCityZone,
  Department,
  Interest,
  KgRequisitionByDepartment,
  CompileList,
} = require("../../hierarchy/models");
const constants = require("../../../constants");
const { isActiveMiqaatAndArazCity } = require("../../../utils/checkActiveMiqaatAndArazcityStatus");

const getZoneWiseReport = apiHandler(async (req, res) => {
  const { arazCityZoneID, miqaatID, arazCityID } = req.body;

  const checkActiveStatus = await isActiveMiqaatAndArazCity(miqaatID, arazCityID, req);
  if (!checkActiveStatus) {
    return apiError(CUSTOM_ERROR, "Araz City is not active", null, res);
  }

  // Populate the departments.departmentID to get department details
  const arazCity = await ArazCity.findById(arazCityID).populate({
    path: "departments.departmentID",
    select: "name uniqueName LDName",
  });

  if (isEmpty(arazCity)) {
    return apiError(NOT_FOUND, "Araz City", null, res);
  }

  // Filter departments based on zone
  const departments = arazCity.departments.filter((dept) => {
    if (arazCityZoneID !== constants.ARAZ_CITY_ZONES.CMZ[0].toString()) {
      return dept.status === "active" && dept.isZonal === true;
    }
    return dept.status === "active";
  });

  const departmentIds = departments.map((dept) => dept.departmentID._id);

  // Get all requirements for these departments
  const requirements = await KgRequisitionByDepartment.find({
    departmentID: { $in: departmentIds },
    miqaatID: toObjectId(miqaatID),
    arazCityID: toObjectId(arazCityID),
    arazCityZoneID: toObjectId(arazCityZoneID),
  })
    .populate("departmentID", "name uniqueName LDName")
    .lean();

  const departmentWithRequirements = await Promise.all(
    departments.map(async (dept) => {
      const deptId = dept.departmentID._id.toString();
      const deptRequirements = requirements.filter(
        (req) => req.departmentID._id.toString() === deptId
      );

      // Count already assigned users
      const maleUserCount = await KGUser.countDocuments({
        gender: "M",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID,
            departmentID: deptId,
          },
        },
      });

      const femaleUserCount = await KGUser.countDocuments({
        gender: "F",
        miqaats: {
          $elemMatch: {
            isActive: true,
            miqaatID,
            arazCityID,
            arazCityZoneID,
            departmentID: deptId,
          },
        },
      });

      // Count users in KG Pool with this department as their first priority
      const maleUserCountInKGPool = await Interest.aggregate([
        {
          $match: {
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
            "interestOne.departmentID": toObjectId(deptId),
            status: "not-assigned",
            arazCityZoneID: toObjectId(arazCityZoneID),
          },
        },
        {
          $lookup: {
            from: "kgusers",
            localField: "userID",
            foreignField: "_id",
            as: "user",
          },
        },
        { $unwind: "$user" },
        {
          $match: { "user.gender": "M" },
        },
        { $count: "total" },
      ]);

      const femaleUserCountInKGPool = await Interest.aggregate([
        {
          $match: {
            miqaatID: toObjectId(miqaatID),
            arazCityID: toObjectId(arazCityID),
            "interestOne.departmentID": toObjectId(deptId),
            arazCityZoneID: toObjectId(arazCityZoneID),
            status: "not-assigned",
          },
        },
        {
          $lookup: {
            from: "kgusers",
            localField: "userID",
            foreignField: "_id",
            as: "user",
          },
        },
        { $unwind: "$user" },
        {
          $match: { "user.gender": "F" },
        },
        { $count: "total" },
      ]);

      const requirement = deptRequirements[0];

      const maleKGPoolCount = maleUserCountInKGPool[0]?.total || 0;
      const femaleKGPoolCount = femaleUserCountInKGPool[0]?.total || 0;

      const requiredMardoKgCount = requirement?.requiredMardoKgCount || 0;
      const requiredBairaoKgCount = requirement?.requiredBairaoKgCount || 0;

      return {
        departmentID: dept.departmentID._id,
        departmentName: dept.departmentID.name,
        totalRequiredKgCount: requiredMardoKgCount + requiredBairaoKgCount,
        requiredMardoKgCount,
        requiredBairaoKgCount,
        alreadyAssignedMardo: maleUserCount || 0,
        alreadyAssignedBairao: femaleUserCount || 0,
        presentInKgPoolMardo: maleKGPoolCount,
        presentInKgPoolBairao: femaleKGPoolCount,
        shortageMardo: requiredMardoKgCount - (maleUserCount || 0),
        shortageBairao: requiredBairaoKgCount - (femaleUserCount || 0),
      };
    })
  );

  departmentWithRequirements.sort((a, b) =>
    a.departmentName.localeCompare(b.departmentName)
  );

  // Calculate the summary object
  const summary = {
    departmentName: "All",
    totalRequiredKgCount: 0,
    requiredMardoKgCount: 0,
    requiredBairaoKgCount: 0,
    alreadyAssignedMardo: 0,
    alreadyAssignedBairao: 0,
    presentInKgPoolMardo: 0,
    presentInKgPoolBairao: 0,
    shortageMardo: 0,
    shortageBairao: 0,
  };
  const maleUserCountZoneNotAssigned = await KGUser.countDocuments({
    gender: "M",
    miqaats: {
      $elemMatch: {
        isActive: true,
        miqaatID,
        arazCityID,
        arazCityZoneID,
        $or: [{ departmentID: null }, { departmentID: { $exists: false } }],
      },
    },
  });

  const femaleUserCountZoneNotAssigned = await KGUser.countDocuments({
    gender: "F",
    miqaats: {
      $elemMatch: {
        isActive: true,
        miqaatID,
        arazCityID,
        arazCityZoneID,
        $or: [{ departmentID: null }, { departmentID: { $exists: false } }],
      },
    },
  });

  const maleUserCountInKGPool = await Interest.aggregate([
    {
      $match: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        status: "not-assigned",
        arazCityZoneID: toObjectId(arazCityZoneID),
        $or: [
          { "interestOne.departmentID": null },
          { "interestOne.departmentID": { $exists: false } },
        ],
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "userID",
        foreignField: "_id",
        as: "user",
      },
    },
    { $unwind: "$user" },
    {
      $match: { "user.gender": "M" },
    },
    { $count: "total" },
  ]);

  const femaleUserCountInKGPool = await Interest.aggregate([
    {
      $match: {
        miqaatID: toObjectId(miqaatID),
        arazCityID: toObjectId(arazCityID),
        $or: [
          { "interestOne.departmentID": null },
          { "interestOne.departmentID": { $exists: false } },
        ],
        arazCityZoneID: toObjectId(arazCityZoneID),
        status: "not-assigned",
      },
    },
    {
      $lookup: {
        from: "kgusers",
        localField: "userID",
        foreignField: "_id",
        as: "user",
      },
    },
    { $unwind: "$user" },
    {
      $match: { "user.gender": "F" },
    },
    { $count: "total" },
  ]);

  const departmentNA = {
    departmentName: "Department NA",
    totalRequiredKgCount: 0,
    requiredMardoKgCount: 0,
    requiredBairaoKgCount: 0,
    alreadyAssignedMardo: maleUserCountZoneNotAssigned,
    alreadyAssignedBairao: femaleUserCountZoneNotAssigned,
    presentInKgPoolMardo: maleUserCountInKGPool[0]?.total || 0,
    presentInKgPoolBairao: femaleUserCountInKGPool[0]?.total || 0,
    shortageMardo: -maleUserCountZoneNotAssigned,
    shortageBairao:-femaleUserCountZoneNotAssigned,
  };

  departmentWithRequirements.push(departmentNA);
  for (const dept of departmentWithRequirements) {
    summary.totalRequiredKgCount += dept.totalRequiredKgCount;
    summary.requiredMardoKgCount += dept.requiredMardoKgCount;
    summary.requiredBairaoKgCount += dept.requiredBairaoKgCount;
    summary.alreadyAssignedMardo += dept.alreadyAssignedMardo;
    summary.alreadyAssignedBairao += dept.alreadyAssignedBairao;
    summary.presentInKgPoolMardo += dept.presentInKgPoolMardo;
    summary.presentInKgPoolBairao += dept.presentInKgPoolBairao;
    summary.shortageMardo += dept.shortageMardo;
    summary.shortageBairao += dept.shortageBairao;
  }

  departmentWithRequirements.unshift(summary);

  return apiResponse(
    FETCH,
    "Zone-wise KG Requisitions",
    departmentWithRequirements,
    res
  );
});

module.exports = {
  getZoneWiseReport,
};
