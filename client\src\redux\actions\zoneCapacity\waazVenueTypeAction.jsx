import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityWaazVenueTypeApiUrl } from 'utils/constant';

const getWaazVenueTypeURL = (endponints) => {
  return `${ZoneCapacityWaazVenueTypeApiUrl}${endponints}`;
};

const getAllWaazVenueType = (data, dispatch) => {
  return getRequest('global', getWaazVenueTypeURL('get'), data, true, dispatch);
};

const getSingleWaazVenueType = (data, dispatch) => {
  return getRequest('global', getWaazVenueTypeURL(`get/${data}`), data, true, dispatch);
};

const addWaazVenueType = (data, dispatch) => {
  return postRequest('global', getWaazVenueTypeURL('add'), data, true, dispatch);
};

const updateWaazVenueType = (data, dispatch) => {
  return putRequest('global', getWaazVenueTypeURL('edit'), data, true, dispatch);
};

const deleteWaazVenueType = (data, dispatch) => {
  return deleletRequest('global', getWaazVenueTypeURL(`delete/${data}`), data, true, dispatch);
};

export const getAllWaazVenueTypeAction = createGenericAsyncThunk('waazVenueType/getAllWaazVenueTypeAction', getAllWaazVenueType, 'get');

export const getSingleWaazVenueTypeAction = createGenericAsyncThunk(
  'waazVenueType/getSingleWaazVenueTypeAction',
  getSingleWaazVenueType,
  'get'
);

export const addWaazVenueTypeAction = createGenericAsyncThunk('waazVenueType/addWaazVenueTypeAction', addWaazVenueType);

export const updateWaazVenueTypeAction = createGenericAsyncThunk('waazVenueType/updateWaazVenueTypeAction', updateWaazVenueType);

export const deleteWaazVenueTypeAction = createGenericAsyncThunk('waazVenueType/deleteWaazVenueTypeAction', deleteWaazVenueType);
