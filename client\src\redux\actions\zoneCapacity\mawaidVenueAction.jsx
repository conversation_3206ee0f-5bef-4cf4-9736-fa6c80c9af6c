import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, patchRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityMawaidVenueApiUrl } from 'utils/constant';

const getMawaidVenueURL = (endponints) => {
  return `${ZoneCapacityMawaidVenueApiUrl}${endponints}`;
};

const getAllMawaidVenue = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL('get'), data, true, dispatch);
};

const getSingleMawaidVenue = (data, dispatch) => {
  return getRequest('zonesCapacity', getMawaidVenueURL(`get/${data}`), data, true, dispatch);
};

const addMawaidVenue = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL('add'), data, true, dispatch);
};

const updateMawaidVenue = (data, dispatch) => {
  return putRequest('zonesCapacity', getMawaidVenueURL('edit'), data, true, dispatch);
};

const deleteMawaidVenue = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getMawaidVenueURL(`delete/${data}`), data, true, dispatch);
};

const getMawaidDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL(`upload/${data?.functionName}/get`), data, true, dispatch);
};

const getMawaidMasterDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL(`upload/file/get-master-files`), data, true, dispatch);
};

const uploadMawaidDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL(`upload/newFile`), data, true, dispatch, 'formdata');
};

const viewMawaidDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', getMawaidVenueURL(`get/download-url`), data, true, dispatch);
};

const updateMawaidDrwaingFileStatus = (data, dispatch) => {
  let { functionName, ...rest } = data || {};
  let payload = { ...rest };
  return putRequest('zonesCapacity', getMawaidVenueURL(`approve/${data?.functionName}`), payload, true, dispatch);
};

const deleteMawaidDrawingFile = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getMawaidVenueURL(`upload/${data?.functionName}/delete/${data?.fileId}`), data, true, dispatch);
};

const activateMawaidVenue = (data, dispatch) => {
  return patchRequest('zonesCapacity', 'zones-capacity/mawaid-venue/activate-mawaid-venue', data, true, dispatch);
};

const storeFinalCapacity = (data, dispatch) => {
  return patchRequest('zonesCapacity', 'zones-capacity/mawaid-venue/final-capacity', data, true, dispatch);
};

export const getAllMawaidVenueAction = createGenericAsyncThunk('mawaidVenue/getAllMawaidVenueAction', getAllMawaidVenue, 'get');

export const getSingleMawaidVenueAction = createGenericAsyncThunk('mawaidVenue/getSingleMawaidVenueAction', getSingleMawaidVenue, 'get');

export const addMawaidVenueAction = createGenericAsyncThunk('mawaidVenue/addMawaidVenueAction', addMawaidVenue);

export const updateMawaidVenueAction = createGenericAsyncThunk('mawaidVenue/updateMawaidVenueAction', updateMawaidVenue);

export const deleteMawaidVenueAction = createGenericAsyncThunk('mawaidVenue/deleteMawaidVenueAction', deleteMawaidVenue);

export const getMawaidDrawingFileAction = createGenericAsyncThunk('MawaidVenue/getDrawingFileAction', getMawaidDrawingFile);

export const getMawaidMasterDrawingFileAction = createGenericAsyncThunk(
  'MawaidVenue/getMasterDrawingFileAction',
  getMawaidMasterDrawingFile
);

export const uploadMawaidDrawingFileAction = createGenericAsyncThunk('MawaidVenue/uploadMawaidDrawingFileAction', uploadMawaidDrawingFile);

export const activateMawaidVenueAction = createGenericAsyncThunk('MawaidVenue/activateMawaidVenueAction', activateMawaidVenue);

export const storeFinalCapacityAction = createGenericAsyncThunk('MawaidVenue/storeFinalCapacityAction', storeFinalCapacity);

export const viewMawaidDrawingileAction = createGenericAsyncThunk('MawaidVenue/viewMawaidDrawingFile', viewMawaidDrawingFile, 'get');

export const updateMawaidDrwaingFileStatusAction = createGenericAsyncThunk(
  'MawaidVenue/updateMawaidDrwaingFileStatusAction',
  updateMawaidDrwaingFileStatus
);

export const deleteMawaidDrwaingFileAction = createGenericAsyncThunk('MawaidVenue/deleteDrwaingFileAction', deleteMawaidDrawingFile);
