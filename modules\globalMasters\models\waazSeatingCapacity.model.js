const { Schema, model } = require("mongoose");

const waazSeatingCapacity = new Schema(
  {
    waazVenueTypeID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenueType",
      required: true,
    },
    waazVenueSuitabilityID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenueSuitability",
      required: true,
    },
    waazVenuePriorityID: {
      type: Schema.Types.ObjectId,
      ref: "WaazVenuePriority",
      required: true,
    },
    multiplicationFactor: {
      type: Number,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

const WaazSeatingCapacity = model("WaazSeatingCapacity", waazSeatingCapacity);

module.exports = { WaazSeatingCapacity };
