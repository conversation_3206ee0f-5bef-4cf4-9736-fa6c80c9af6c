const {
  apiHandler,
  apiResponse,
  apiError,
} = require("../../../utils/api.util");
const {
  FETCH,
  ADD_SUCCESS,
  NOT_FOUND,
  DELETE_SUCCESS,
  UPDATE_SUCCESS,
  CUSTOM_SUCCESS,
  CUSTOM_ERROR,
} = require("../../../utils/message.util");
const { WaazSeatingCapacity } = require("../models/waazSeatingCapacity.model");

const getMultiplicationFactor = apiHandler(async (req, res) => {
  const { waazVenueTypeID, waazVenueSuitabilityID, waazVenuePriorityID } =
    req.query;
  if (!waazVenueTypeID || !waazVenueSuitabilityID || !waazVenuePriorityID) {
    return apiError(
      CUSTOM_ERROR,
      "Please provide all required parameters: waazVenueTypeID, waazVenueSuitabilityID, and waazVenuePriorityID",
      null,
      res
    );
  }

  const seatingCapacity = await WaazSeatingCapacity.findOne({
    waazVenueTypeID,
    waazVenueSuitabilityID,
    waazVenuePriorityID,
  }).lean();

  if (!seatingCapacity) {
    return apiError(
      NOT_FOUND,
      "No multiplication factor found for the provided combination",
      null,
      res
    );
  }

  return apiResponse(
    FETCH,
    "Multiplication Factor",
    { multiplicationFactor: seatingCapacity.multiplicationFactor },
    res
  );
});

const getAllWaazSeatingCapacities = apiHandler(async (req, res) => {
  const waazSeatingCapacities = await WaazSeatingCapacity.find()
    .populate("waazVenueTypeID", "name")
    .populate("waazVenueSuitabilityID", "name")
    .populate("waazVenuePriorityID", "name")
    .lean()
    .sort({ _id: -1 });

  return apiResponse(
    FETCH,
    "Waaz Seating Capacities",
    waazSeatingCapacities,
    res
  );
});

const getSingleWaazSeatingCapacity = apiHandler(async (req, res) => {
  const { id } = req.params;

  const waazSeatingCapacity = await WaazSeatingCapacity.findById(id)
    .populate("waazVenueTypeID", "name")
    .populate("waazVenueSuitabilityID", "name")
    .populate("waazVenuePriorityID", "name")
    .lean();

  if (!waazSeatingCapacity) {
    return apiError(NOT_FOUND, "Waaz Seating Capacity", null, res);
  }

  return apiResponse(FETCH, "Waaz Seating Capacity", waazSeatingCapacity, res);
});

const addEditWaazSeatingCapacity = apiHandler(async (req, res) => {
  const {
    _id,
    waazVenueTypeID,
    waazVenueSuitabilityID,
    waazVenuePriorityID,
    multiplicationFactor,
  } = req.body;

  if (!_id) {
    const existingCapacity = await WaazSeatingCapacity.findOne({
      waazVenueTypeID,
      waazVenueSuitabilityID,
      waazVenuePriorityID,
    });

    if (existingCapacity) {
      return apiError(
        CUSTOM_ERROR,
        "A Waaz Seating Capacity with this combination already exists",
        null,
        res
      );
    }

    const newCapacity = new WaazSeatingCapacity({
      waazVenueTypeID,
      waazVenueSuitabilityID,
      waazVenuePriorityID,
      multiplicationFactor,
      createdBy: req.user._id,
    });

    const savedCapacity = await newCapacity.save();

    return apiResponse(
      ADD_SUCCESS,
      "Waaz Seating Capacity",
      savedCapacity,
      res
    );
  } else {
    const existingCapacity = await WaazSeatingCapacity.findById(_id);

    if (!existingCapacity) {
      return apiError(NOT_FOUND, "Waaz Seating Capacity", null, res);
    }

    const duplicateCapacity = await WaazSeatingCapacity.findOne({
      waazVenueTypeID,
      waazVenueSuitabilityID,
      waazVenuePriorityID,
      _id: { $ne: _id },
    });

    if (duplicateCapacity) {
      return apiError(
        CUSTOM_ERROR,
        "A Waaz Seating Capacity with this combination already exists",
        null,
        res
      );
    }

    const updatedCapacity = await WaazSeatingCapacity.findByIdAndUpdate(
      _id,
      {
        waazVenueTypeID,
        waazVenueSuitabilityID,
        waazVenuePriorityID,
        multiplicationFactor,
        updatedBy: req.user._id,
      },
      { new: true, runValidators: true }
    );

    return apiResponse(
      UPDATE_SUCCESS,
      "Waaz Seating Capacity",
      updatedCapacity,
      res
    );
  }
});

const deleteWaazSeatingCapacity = apiHandler(async (req, res) => {
  const { id } = req.params;

  const waazSeatingCapacity = await WaazSeatingCapacity.findOneAndDelete({
    _id: id,
  });

  if (!waazSeatingCapacity) {
    return apiError(NOT_FOUND, "Waaz Seating Capacity", null, res);
  }

  return apiResponse(DELETE_SUCCESS, "Waaz Seating Capacity", null, res);
});

module.exports = {
  getAllWaazSeatingCapacities,
  getSingleWaazSeatingCapacity,
  addEditWaazSeatingCapacity,
  deleteWaazSeatingCapacity,
  getMultiplicationFactor
};
