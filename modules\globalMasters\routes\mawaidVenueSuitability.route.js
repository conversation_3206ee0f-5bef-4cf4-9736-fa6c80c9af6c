const {
  getAllMawaidVenueSuitabilities,
  addEditMawaidVenueSuitability,
  getSingleMawaidVenueSuitability,
  deleteMawaidVenueSuitability,
} = require("../controllers/mawaidVenueSuitability.controller");
const {
  addMawaidVenueSuitabilitySchema,
  getSingleMawaidVenueSuitabilitySchema,
  editMawaidVenueSuitabilitySchema,
  deleteMawaidVenueSuitabilitySchema,
} = require("../validations/mawaidVenueSuitability.validation");
const { validate } = require("../../../middlewares/validation.middleware");

const router = require("express").Router();

router.get("/get", getAllMawaidVenueSuitabilities);

router.post(
  "/add",
  validate(addMawaidVenueSuitabilitySchema, "body"),
  addEditMawaidVenueSuitability
);

router.get(
  "/get/:id",
  validate(getSingleMawaidVenueSuitabilitySchema, "params"),
  getSingleMawaidVenueSuitability
);

router.put(
  "/edit",
  validate(editMawaidVenueSuitabilitySchema, "body"),
  addEditMawaidVenueSuitability
);

router.delete(
  "/delete/:id",
  validate(deleteMawaidVenueSuitabilitySchema, "params"),
  deleteMawaidVenueSuitability
);

module.exports = router;
