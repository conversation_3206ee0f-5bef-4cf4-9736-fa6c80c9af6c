const { spawn } = require("child_process");
const path = require("path");
const { NODE_ENV } = require("./constants");

// Define all microservices
const services = [
  {
    name: "Gateway",
    script: "server.js",
    port: process.env.GATEWAY_PORT || 8000,
  },
  {
    name: "Global",
    script: "services/global/global.service.js",
    port: process.env.GLOBAL_PORT || 8001,
  },
  {
    name: "Hierarchy",
    script: "services/hierarchy.service.js",
    port: process.env.HIERARCHY_PORT || 8002,
  },
  {
    name: "Communication",
    script: "services/communication.service.js",
    port: process.env.COMMUNICATION_PORT || 8003,
  },
  {
    name: "Zones Capacity",
    script: "services/zonesCapacity.service.js",
    port: process.env.ZONES_CAPACITY_PORT || 8005,
  },
  {
    name: "Task Management",
    script: "services/taskmanagement.service.js",
    port: process.env.TASK_MANAGEMENT_PORT || 8006,
  },
  {
    name: "Survey",
    script: "services/survey.service.js",
    port: process.env.SURVEY_PORT || 8010,
  },
  {
    name: "Auth Service",
    script: "services/user.service.js",
    port: process.env.AUTH_SERVICE_PORT || 8012,
  },
];

const processes = [];

// Start one service
function startService(service) {
  console.log(`🔹 Starting ${service.name} on port ${service.port}...`);

  const cmd = NODE_ENV === "production" ? "node" : "npx";
  const args =
    NODE_ENV === "production"
      ? ["--max-old-space-size=2560", service.script]
      : ["nodemon", "--max-old-space-size=2560", service.script];

  const child = spawn(cmd, args, {
    stdio: "inherit",
    cwd: __dirname,
    shell: true, // This helps resolve npx on Windows
  });

  child.on("error", (err) => {
    console.error(`❌ Failed to start ${service.name}:`, err);
  });

  child.on("exit", (code) => {
    console.log(`⚠️ ${service.name} exited with code ${code}`);
  });

  processes.push({ name: service.name, process: child });
}

// Graceful shutdown
function stopAllServices() {
  console.log("\n🛑 Stopping all services...");
  processes.forEach(({ name, process }) => {
    console.log(`↪️ Stopping ${name}...`);
    process.kill("SIGTERM");
  });
  process.exit(0);
}

process.on("SIGINT", stopAllServices);
process.on("SIGTERM", stopAllServices);

// Async function to launch services with delay
async function startAll() {
  console.log("🚀 Starting AMS Microservices...\n");

  for (const service of services) {
    startService(service);
    await new Promise((res) => setTimeout(res, 1000)); // Delay between starts
  }

  console.log("\n✅ All services started. Press Ctrl+C to stop.\n");
  console.log("🌐 Service URLs:");
  services.forEach((service) => {
    console.log(`${service.name}: http://localhost:${service.port}`);
  });
  console.log("");
}

startAll();
