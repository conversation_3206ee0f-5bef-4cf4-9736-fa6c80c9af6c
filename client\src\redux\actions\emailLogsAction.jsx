import dayjs from 'dayjs';
import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';

import { CommunicationEmailLogApiUrl, CommunicationEmailReportApiUrl } from 'utils/constant';

const getEmailReportURL = (endponints) => {
  return `${CommunicationEmailLogApiUrl}${endponints}`;
};

const getEmailReport = (data, dispatch) => {
  return postRequest('communication', getEmailReportURL('get'), data, true, dispatch);
};

const getSingleEmailLog = (data, dispatch) => {
  return getRequest('communication', getEmailReportURL(`get/${data}`), data, true, dispatch);
};

const getRecipientList = (data, dispatch) => {
  return getRequest('communication', getEmailReportURL(`get/recipients/${data}`), data, true, dispatch);
};

export const getEmailLogsAction = createGenericAsyncThunk('emailReport/getEmailLogsAction', getEmailReport, 'get', false);

export const getSingleEmailLogAction = createGenericAsyncThunk('emailReport/getSingleEmailLogAction', getSingleEmailLog, 'get', false);

export const getRecipientListAction = createGenericAsyncThunk('emailReport/getRecipientListAction', getRecipientList, 'get', false);
