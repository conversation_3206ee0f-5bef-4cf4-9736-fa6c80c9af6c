const Joi = require("joi");
const {
  stringValidation,
  booleanValidation,
  numberValidation,
  urlValidation,
  dateValidation,
  arrayObjectValidation,
  idValidation,
  emailValidation,
} = require("../../../utils/validator.util");

const addKGGroupSchema = Joi.object({
  name: stringValidation,
});

const getSingleKGGroupSchema = Joi.object({
  id: idValidation,
});

const editKGGroupSchema = Joi.object({
  id: idValidation,
  name: stringValidation,
});

const deleteKGGroupSchema = Joi.object({
  id: idValidation,
});

module.exports = {
  addKGGroupSchema,
  getSingleKGGroupSchema,
  editKGGroupSchema,
  deleteKGGroupSchema,
};
