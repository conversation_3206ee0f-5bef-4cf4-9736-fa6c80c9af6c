import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { ZoneCapacityZoneApiUrl, HierarchyZoneReportApiUrl } from 'utils/constant';
import { getArazCityURL } from './arazCityAction';
const getZoneURL = (endponints) => {
  return `${ZoneCapacityZoneApiUrl}${endponints}`;
};

const getDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', `hierarchy/araz-city-zone-file/get`, data, true, dispatch);
};

const getDrawingFunction = (data, dispatch) => {
  return getRequest('zonesCapacity', `zones-capacity/global-function/get`, data, true, dispatch);
};

const uploadDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', 'hierarchy/araz-city-zone-file/upload', data, true, dispatch, 'formdata');
};

const viewDrawingFile = (data, dispatch) => {
  return postRequest('zonesCapacity', 'hierarchy/araz-city-zone-file/get/download-url', data, true, dispatch);
};

const updateDrwaingFileStatus = (data, dispatch) => {
  return putRequest('zonesCapacity', 'hierarchy/araz-city-zone-file/update/status', data, true, dispatch);
};

const deleteDrawingFile = (data, dispatch) => {
  return deleletRequest('zonesCapacity', `/hierarchy/araz-city-zone-file/delete/${data}`, data, true, dispatch);
};

export const getDrawingFileAction = createGenericAsyncThunk('arazCityZone/getDrawingFileAction', getDrawingFile);

export const uploadDrawingFileAction = createGenericAsyncThunk('arazCityZone/uploadDrawingFileAction', uploadDrawingFile);

export const getDrawingFunctionsAction = createGenericAsyncThunk('arazCityZone/getDrawingFunctionsAction', getDrawingFunction, 'get');

export const viewDrawingileAction = createGenericAsyncThunk('arazCityZone/viewDrawingFile', viewDrawingFile, 'get');

export const updateDrwaingFileStatusAction = createGenericAsyncThunk('arazCityZone/updateDrwaingFileStatusAction', updateDrwaingFileStatus);

export const deleteDrwaingFileAction = createGenericAsyncThunk('arazCityZone/deleteDrwaingFileAction', deleteDrawingFile);
