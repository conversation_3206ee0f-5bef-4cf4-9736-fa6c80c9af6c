/**
 * API Gateway
 * 
 * Main gateway application that routes requests to microservices
 * Runs on port 8000 and acts as a single entry point for all services
 */

const express = require('express');
const helmet = require('helmet');
const compression = require('compression');
const ServiceDiscovery = require('./serviceDiscovery');
const { GATEWAY_CONFIG } = require('./config');
const {
  rateLimiter,
  gatewayAuth,
  requestLogger,
  createServiceProxy,
  errorHandler,
  corsMiddleware,
  requestId
} = require('./middleware');

class APIGateway {
  constructor() {
    this.app = express();
    this.serviceDiscovery = null;
    this.server = null;
    
    this.initializeMiddleware();
    this.initializeServiceDiscovery();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize middleware
   */
  initializeMiddleware() {
    console.log('🔧 Initializing gateway middleware...');
    
    // Security middleware
    this.app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: false
    }));
    
    // CORS
    this.app.use(corsMiddleware);
    
    // Request ID
    this.app.use(requestId);
    
    // Compression
    this.app.use(compression());
    
    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Rate limiting
    this.app.use(rateLimiter);
    
    // Request logging
    this.app.use(requestLogger);
    
    console.log('✅ Gateway middleware initialized');
  }

  /**
   * Initialize service discovery
   */
  initializeServiceDiscovery() {
    console.log('🔍 Initializing service discovery...');
    this.serviceDiscovery = new ServiceDiscovery();
    console.log('✅ Service discovery initialized');
  }

  /**
   * Initialize routes
   */
  initializeRoutes() {
    console.log('🛣️  Initializing gateway routes...');
    
    // Gateway health check
    this.app.get('/gateway/health', (req, res) => {
      res.json({
        success: true,
        message: 'Gateway is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0'
      });
    });
    
    // Gateway status
    this.app.get('/gateway/status', (req, res) => {
      const servicesStatus = this.serviceDiscovery.getServicesStatus();
      
      res.json({
        success: true,
        gateway: {
          status: 'running',
          port: GATEWAY_CONFIG.port,
          uptime: process.uptime(),
          timestamp: new Date().toISOString()
        },
        services: servicesStatus
      });
    });
    
    // List all services
    this.app.get('/gateway/services', (req, res) => {
      const servicesStatus = this.serviceDiscovery.getServicesStatus();
      
      const servicesList = Object.entries(servicesStatus).map(([key, service]) => ({
        name: key,
        displayName: service.name,
        healthy: service.healthyCount > 0,
        instances: service.totalCount,
        healthyInstances: service.healthyCount,
        routes: this.serviceDiscovery.services.get(key)?.routes || []
      }));
      
      res.json({
        success: true,
        services: servicesList,
        totalServices: servicesList.length,
        healthyServices: servicesList.filter(s => s.healthy).length
      });
    });
    
    // Gateway metrics
    this.app.get('/gateway/metrics', (req, res) => {
      const memUsage = process.memoryUsage();
      
      res.json({
        success: true,
        metrics: {
          uptime: process.uptime(),
          memory: {
            rss: Math.round(memUsage.rss / 1024 / 1024) + ' MB',
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + ' MB',
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + ' MB',
            external: Math.round(memUsage.external / 1024 / 1024) + ' MB'
          },
          cpu: process.cpuUsage(),
          pid: process.pid,
          platform: process.platform,
          nodeVersion: process.version
        },
        timestamp: new Date().toISOString()
      });
    });
    
    // Authentication middleware for protected routes
    this.app.use('/api', gatewayAuth);
    
    // Service proxy middleware - this handles all /api/* routes
    this.app.use('/api', createServiceProxy(this.serviceDiscovery));
    
    // Handle webhook routes (public)
    this.app.use('/api/webhook', createServiceProxy(this.serviceDiscovery));
    
    // Handle public API routes
    this.app.use('/api/v1', createServiceProxy(this.serviceDiscovery));
    
    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        success: true,
        message: 'AMS API Gateway',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/gateway/health',
          status: '/gateway/status',
          services: '/gateway/services',
          metrics: '/gateway/metrics'
        }
      });
    });
    
    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
      });
    });
    
    console.log('✅ Gateway routes initialized');
  }

  /**
   * Initialize error handling
   */
  initializeErrorHandling() {
    this.app.use(errorHandler);
  }

  /**
   * Start the gateway server
   */
  async start() {
    try {
      console.log('🚀 Starting API Gateway...');
      
      this.server = this.app.listen(GATEWAY_CONFIG.port, () => {
        console.log(`✅ API Gateway running on port ${GATEWAY_CONFIG.port}`);
        console.log(`🌐 Gateway URL: http://localhost:${GATEWAY_CONFIG.port}`);
        console.log(`📊 Health Check: http://localhost:${GATEWAY_CONFIG.port}/gateway/health`);
        console.log(`📈 Status: http://localhost:${GATEWAY_CONFIG.port}/gateway/status`);
        console.log(`🔍 Services: http://localhost:${GATEWAY_CONFIG.port}/gateway/services`);
      });
      
      // Handle server errors
      this.server.on('error', (error) => {
        console.error('❌ Gateway server error:', error);
      });
      
      // Graceful shutdown
      this.setupGracefulShutdown();
      
    } catch (error) {
      console.error('❌ Failed to start API Gateway:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      
      // Stop accepting new connections
      if (this.server) {
        this.server.close(() => {
          console.log('✅ HTTP server closed');
        });
      }
      
      // Stop service discovery
      if (this.serviceDiscovery) {
        this.serviceDiscovery.stop();
      }
      
      console.log('✅ Gateway shutdown complete');
      process.exit(0);
    };
    
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
  }

  /**
   * Stop the gateway
   */
  async stop() {
    if (this.server) {
      this.server.close();
    }
    
    if (this.serviceDiscovery) {
      this.serviceDiscovery.stop();
    }
  }
}

// Create and start gateway if this file is run directly
if (require.main === module) {
  const gateway = new APIGateway();
  gateway.start();
}

module.exports = APIGateway;
