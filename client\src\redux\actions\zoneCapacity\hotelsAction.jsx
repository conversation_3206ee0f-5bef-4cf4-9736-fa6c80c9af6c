import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HotelsApiUrl } from 'utils/constant';

const getHotelURL = (endponints) => {
  return `${HotelsApiUrl}${endponints}`;
};

const getAllHotels = (data, dispatch) => {
  // return getRequest(getHotelURL('get'), data, true, dispatch);
  return getRequest('zonesCapacity', getHotelURL(`get?arazCityID=${data?.arazCity}&miqaatID=${data?.miqaat}`), data, true, dispatch);
  // return {
  //   data: {
  //     success: true,
  //     data: [{ name: 'City Lodge', zone: { id: '32432432', name: 'vajihi' } }]
  //   }
  // };
};

const getCountries = (data, dispatch) => {
  return getRequest('zonesCapacity', getHotelURL('get'), data, true, dispatch);
};

const getState = (data, dispatch) => {
  return getRequest('zonesCapacity', getHotelURL('get'), data, true, dispatch);
};

const getSingleHotel = (data, dispatch) => {
  // return getRequest(getHotelURL(`get/${data}`), data, true, dispatch);
  return getRequest('zonesCapacity', getHotelURL(`get/${data}`), data, true, dispatch);
};

const addHotel = (data, dispatch) => {
  return postRequest('zonesCapacity', getHotelURL('add'), data, true, dispatch);
};

const updateHotel = (data, dispatch) => {
  return putRequest('zonesCapacity', getHotelURL('edit'), data, true, dispatch);
};
const uploadImage = (data, dispatch) => {
  return postRequest('zonesCapacity', getHotelURL('upload'), data, true, dispatch);
};
const getImage = (data, dispatch) => {
  return postRequest('zonesCapacity', getHotelURL('get/image/download-url'), data, true, dispatch);
};

const deleteHotel = (data, dispatch) => {
  return deleletRequest('zonesCapacity', getHotelURL(`delete/${data}`), data, true, dispatch);
};

export const getAllHotelsAction = createGenericAsyncThunk('Hotels/getAllHotelsAction', getAllHotels, 'get');

export const getSingleHotelAction = createGenericAsyncThunk('Hotels/getSingleHotelAction', getSingleHotel, 'get');

export const addHotelAction = createGenericAsyncThunk('Hotels/addHotelAction', addHotel);

export const updateHotelAction = createGenericAsyncThunk('Hotels/updateHotelAction', updateHotel);

export const uploadImageAction = createGenericAsyncThunk('Hotels/uploadImageAction', uploadImage);

export const getHotelImageAction = createGenericAsyncThunk('Hotels/getHotelImageAction', getImage);

export const deleteHotelAction = createGenericAsyncThunk('Hotel/deleteHotelAction', deleteHotel);

export const getCountriesAction = createGenericAsyncThunk('Hotel/getCountries', getCountries);

export const getStateAction = createGenericAsyncThunk('Hotel/getState', getState);
