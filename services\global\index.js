const express = require("express");
const router = express.Router();

const globalMastersRoutes = require("../../modules/globalMasters/routes");
const asharaGuideRoutes = require("../../modules/asharaGuide/routes");
const accomodationRoutes = require("../../modules/muqimeenMehmaanMapping/routes");
const documentRoutes = require("../../modules/documentManager/routes");
const reports = require("../../modules/reports/routes");
const analyticsLogRoute = require('../../modules/analyticsLog/routes');

router.use("/global-master", globalMastersRoutes);
router.use("/ashara-guide", asharaGuideRoutes);
router.use("/accomodation", accomodationRoutes);
router.use("/document-manager", documentRoutes);
router.use("/report", reports);
router.use('/activity', analyticsLogRoute);


module.exports = router;
