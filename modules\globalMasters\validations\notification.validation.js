const Joi = require("joi");
const { idValidation, numberValidation, stringValidation, arrayIdValidation } = require("../../../utils/validator.util");

const getNotificationsSchema = Joi.object({
  page: numberValidation,
  limit: numberValidation
});

const getNotificationSchema = Joi.object({
  id: idValidation
});

const sendMeetingReminderSchema = Joi.object({
    meetingID: idValidation,
    type:stringValidation
});

module.exports = {
  getNotificationsSchema,
  getNotificationSchema,
  sendMeetingReminderSchema
}