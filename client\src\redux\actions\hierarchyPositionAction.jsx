import { createGenericAsyncThunk } from 'redux/helper';
import { deleletRequest, getRequest, postRequest, putRequest } from 'utils/axios';
import { HierarchyPositionApiUrl } from 'utils/constant';

const getHierarchyPositionURL = (endponints) => {
  return `${HierarchyPositionApiUrl}${endponints}`;
};

const getAllHierarchyPosition = (data, dispatch) => {
  return getRequest('global', getHierarchyPositionURL('get'), data, true, dispatch);
};

const getSingleHierarchyPosition = (data, dispatch) => {
  return getRequest('global', getHierarchyPositionURL(`get/${data}`), data, true, dispatch);
};

const addHierarchyPosition = (data, dispatch) => {
  return postRequest('global', getHierarchyPositionURL('add'), data, true, dispatch);
};

const updateHierarchyPosition = (data, dispatch) => {
  return putRequest('global', getHierarchyPositionURL('edit'), data, true, dispatch);
};

const deleteHierarchyPosition = (data, dispatch) => {
  return deleletRequest('global', getHierarchyPositionURL(`delete/${data}`), data, true, dispatch);
};

export const getAllHierarchyPositions = createGenericAsyncThunk('KG/getAllHierarchyPositions', getAllHierarchyPosition, 'get');

export const getSingleHierarchyPositionAction = createGenericAsyncThunk(
  'KG/getSingleHierarchyPositionAction',
  getSingleHierarchyPosition,
  'get'
);

export const addHierarchyPositionAction = createGenericAsyncThunk('KG/addHierarchyPositionAction', addHierarchyPosition);

export const updateHierarchyPositionAction = createGenericAsyncThunk('KG/updateHierarchyPositionAction', updateHierarchyPosition);

export const deleteHierarchyPositionAction = createGenericAsyncThunk('KG/deleteHierarchyPositionAction', deleteHierarchyPosition);
