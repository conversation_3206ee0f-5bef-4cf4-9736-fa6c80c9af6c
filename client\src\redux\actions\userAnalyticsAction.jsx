import { createGenericAsyncThunk } from 'redux/helper';
import { getRequest, postRequest } from 'utils/axios';

const postUserAnalytics = (data, dispatch) => {
  console.log('function called with this data', data);
  return postRequest('global', 'global/activity/analytics-log/add', data, dispatch);
};

const getUserAnalytics = (data, dispatch) => {
  return postRequest('global', 'global/activity/analytics-log/get', data, dispatch);
};

export const postUserAnalyticsAction = createGenericAsyncThunk('userAnalytics/postUserAnalytics', postUserAnalytics, 'get');

export const getUserAnalyticsAction = createGenericAsyncThunk('userAnalytics/getUserAnalytics', getUserAnalytics, 'get');
